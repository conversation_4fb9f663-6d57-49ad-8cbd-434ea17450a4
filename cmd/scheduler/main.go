// Package main 程序入口
package main

import (
	"context"
	"fmt"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	_ "git.code.oa.com/trpc-go/trpc-codec/tars"
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	trpchttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "go.uber.org/automaxprocs"

	_ "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/admin/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/checkpoint"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/commvar"
	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/etcd"
	schedulerjob "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/scheduler/cleanup"
	schedulermetric "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/metric/scheduler"
	wkinst "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/instance"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
	taskhistory "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/taskhistory"
	_ "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/service/http/scheduler"
)

// GetETCDLock 取得etcd锁
func GetETCDLock(masterSlaveConfig *schedulerconfig.MasterSlaveConfig) {
	log.Infof("Start get etcd lock ...")
	etcd, err := etcd.NewEtcd(masterSlaveConfig.ETCDUserName, masterSlaveConfig.ETCDPasswd,
		masterSlaveConfig.ETCDAddr, masterSlaveConfig.LockKeyPrefix, 10)
	if err != nil {
		log.Fatalf("Init etcd err %v", err)
		return
	}
	if err := etcd.Campaign(context.Background()); err != nil {
		log.Fatalf("Etcd campaign failed, err=%s", err.Error())
	}
	log.Infof("Get etcd lock success ...")
	return
}

// InitController 初始化
func InitController() {
	// 先初始化公共模块
	checkpoint.Initialize()
	// 再初始化资源模块,对公共模块有依赖
	schedulertask.Initialize()
	wkinst.Initialize()
	taskhistory.Initialize()
	// 最后初始化一些定时任务等,对公共模块和资源模块有依赖
	schedulerjob.SchedulerJobInitialize()
	cleanup.Initialize()
	schedulermetric.Initialize()
}

func main() {
	// NewServer() 调用后才会初始化 trpc-go 日志
	tsrv := trpc.NewServer()
	// 初始化配置和全局公共变量
	schedulerconfig.Initialize()
	commvar.Initialize()
	serverConfig := schedulerconfig.GetConfig()
	// etcd 抢锁
	if serverConfig.MasterSlaveConfig.IsMasterSlave {
		GetETCDLock(&serverConfig.MasterSlaveConfig)
	}
	// 初始化进程其他资源
	InitController()
	// http接口初始化
	muxRouter := schttp.HandleRegisterdFunc()
	trpchttp.RegisterNoProtocolServiceMux(
		tsrv.Service(fmt.Sprintf("trpc.%s.%s.Http", commvar.GetCommVar().App, commvar.GetCommVar().Server)),
		muxRouter)
	// 启动trpc服务
	if err := tsrv.Serve(); err != nil {
		log.Error(err.Error())
	}
	log.Info("====> ending")
}
