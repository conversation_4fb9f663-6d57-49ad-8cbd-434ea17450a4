// Package main 程序入口
package main

import (
	"fmt"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	_ "git.code.oa.com/trpc-go/trpc-codec/tars"
	_ "git.code.oa.com/trpc-go/trpc-config-rainbow"
	_ "git.code.oa.com/trpc-go/trpc-filter/debuglog"
	_ "git.code.oa.com/trpc-go/trpc-filter/recovery"
	"git.code.oa.com/trpc-go/trpc-go"
	trpchttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "git.code.oa.com/trpc-go/trpc-log-atta"
	_ "git.code.oa.com/trpc-go/trpc-metrics-runtime"
	_ "git.code.oa.com/trpc-go/trpc-naming-polaris"
	_ "git.code.oa.com/trpc-go/trpc-opentracing-tjg"
	_ "git.woa.com/galileo/trpc-go-galileo"
	_ "go.uber.org/automaxprocs"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/commvar"
	workerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/worker"
	workerJob "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/worker"
	mmonitor "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/monitor"
	workertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/worker/task"
	_ "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/service/http/worker"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/thirdparty/scheduler"
)

// InitController 初始化
func InitController() {
	// 先初始化公共模块
	scheduler.Initialize()
	workertask.Initialize()
	// 再初始化资源模块,对公共模块有依赖
	// 最后初始化一些定时任务等,对公共模块和资源模块有依赖
	workerJob.WorkerJobInitialize()
}

func main() {
	// NewServer() 调用后才会初始化 trpc-go 日志
	tsrv := trpc.NewServer()
	// 初始化配置和全局公共变量
	workerconfig.Initialize()
	commvar.Initialize()
	mmonitor.Initialize()
	// 初始化进程其他资源
	InitController()
	muxRouter := schttp.HandleRegisterdFunc()
	trpchttp.RegisterNoProtocolServiceMux(
		tsrv.Service(fmt.Sprintf("trpc.%s.%s.Http", commvar.GetCommVar().App, commvar.GetCommVar().Server)),
		muxRouter)
	// 启动trpc服务
	if err := tsrv.Serve(); err != nil {
		log.Error(err.Error())
	}
	log.Info("====> ending")
}
