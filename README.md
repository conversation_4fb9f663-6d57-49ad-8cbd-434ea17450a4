# ConvertScheduler-转模调度服务

## 代码路径
    https://git.woa.com/RondaServing/ServingControllerProjects/ConvertScheduler.git

## 设计文档
    https://doc.weixin.qq.com/doc/w3_AFUAgwaLAFcjySJx904T0yh8cDG5H?scode=AJEAIQdfAAosw6byLPAFUAgwaLAFc

## 服务交互时序图


```mermaid
sequenceDiagram
    participant ServingController
    participant ConvertManager
    participant Workflow
    participant NumerousTensorRT

    ServingController ->> ConvertManager : 提交转模任务
    activate ConvertManager
    ConvertManager ->> ConvertManager: 根据转模参数生成并在DB记录导出模型名
    ConvertManager ->> ConvertManager: DB记录转模任务(生成任务ID)
    ConvertManager ->> Workflow: 提交转模任务(包含任务ID)
    activate Workflow
    Workflow ->> NumerousTensorRT: 提交转模任务(包含任务ID)
    activate NumerousTensorRT
    NumerousTensorRT -->> Workflow: 会话ID
    deactivate NumerousTensorRT
    Workflow -->> ConvertManager: 会话ID
    deactivate Workflow
    ConvertManager -->> ServingController:  导出模型名和版本
    deactivate ConvertManager

    NumerousTensorRT ->> NumerousTensorRT: 失败超时告警


    NumerousTensorRT ->> ConvertManager: 根据任务ID更新单个版本转模状态
    activate ConvertManager
    ConvertManager -->> NumerousTensorRT: OK
    deactivate ConvertManager


    ServingController ->> ConvertManager: 根据导出模型名和版本查询转模任务状态
    activate ConvertManager
    ConvertManager ->> Workflow: 根据会话ID查询组件运行状态
    activate Workflow
    Workflow -->> ConvertManager: 组件运行状态
    deactivate Workflow
    ConvertManager ->> ConvertManager: 更新转模任务状态
    ConvertManager -->> ServingController: 转模任务状态
    deactivate ConvertManager

    ServingController ->> ServingController: 转模失败上报监控告警

    ServingController ->> ServingController: 查询模型

    ServingController ->> ServingController: 查询模型失败上报监控告警

    ServingController ->> ServingController: 上线模型
```

```mermaid
sequenceDiagram
    participant ServingController
    participant ConvertScheduler
    participant ConvertWorker
    
    ServingController ->> ServingController : 接受转模上线请求
    ConvertScheduler ->> ConvertScheduler: 抢锁成为主节点
    ConvertWorker ->> ConvertWorker : Slave启动注册节点
    ServingController ->> ConvertScheduler : 提交转模任务
    activate ConvertScheduler
    ConvertScheduler ->> ConvertScheduler: 记录转模任务
    ConvertScheduler -->> ServingController: 导出模型名和版本
    deactivate ConvertScheduler
    ConvertScheduler->> ConvertScheduler: 定时分配任务
    ConvertWorker ->> ConvertScheduler: 查询任务上报心跳
    activate ConvertScheduler
    ConvertScheduler ->> ConvertWorker: 任务信息
    deactivate ConvertScheduler
    ConvertWorker ->> ConvertWorker : 执行任务
    ConvertWorker ->> ConvertScheduler: 上报任务执行状态
    activate ConvertScheduler
    ConvertScheduler ->> ConvertWorker:ok
    deactivate ConvertScheduler
    ServingController ->> ConvertScheduler: 根据导出模型名和版本查询转模任务状态
    activate ConvertScheduler
    ConvertScheduler -->> ServingController: 转模任务状态
    deactivate ConvertScheduler
    ServingController ->> ServingController: 查询上线模型
```



## 任务状态流转
- 用户提交转模任务->待分配
- Scheduler定时分配任务：待分配->待处理
- Worker超时未接收任务：待处理->待分配
- Worker超时未接收任务且任务超时：待分配->失败
- Worker接收任务：待处理->处理中
- Worker转模完成：处理中->成功
- Worker转模失败且超时：处理中->失败
- Worker转模失败且还未超时：处理中->待分配

```mermaid
stateDiagram-v2
    [*]-->待分配
    待分配-->待处理
    待处理-->待分配:未接收重试
    待分配-->失败:超时
    待处理-->处理中
    待处理-->失败:超时
    处理中-->成功
    处理中-->失败:失败且超时
    处理中-->待分配:失败重试
    成功-->[*]
    失败-->[*]
```
    

## 系统功能
- 任务提交：支持用户提交模型转换任务，系统记录转模任务，不再转发给转模组件。
- 任务队列：当所有节点都在处理任务时，新提交的任务进入队列等待。 
- 任务分配：系统根据任务的GPU卡型、卡数、trt版本等需求，将任务分配给合适的节点，每个节点同时只能处理一个任务。
- 优先分配：对于上线间隔小、转模耗时长的任务优先分配，尽量避免转模超时导致上线失败。
- 专用节点：对于新闻等对转模耗时要求极高的应用组，需要支持专用节点。
- 转模状态：支持查询展示任务的状态：等待、转模中、成功、失败等，转模中状态支持展示任务进度，转模失败支持展示错误原因及处理建议。
- 自动重试：系统应具备容错机制，确保节点迁移删除重启等导致任务失败时能够重新分配或重试。对于上线已经失败的模型，不需要再重试了。
- 自动迁移：对于失去心跳节点或转模多次失败的节点，告警并迁移。
- 系统日志：系统尽可能仅输出关键性日志，当前转模组件日志过长。
- 自动扩容：当队列中的任务数量超过一定阈值时，系统能够自动扩容，增加新的节点以处理更多的任务，如果扩不出来需要告警。
- 自动缩容：每天按机器类型分类统计，仅保留每天最大并发的节点数和少量冗余。
- 灰度切换：支持一键转模灰度从组件转模切换到服务转模。
- 成本结算：对于专用节点和公共节点，按使用量分摊成本。
- 监控告警：等待中、处理中任务数；已完成任务信息；已使用、空闲资源数。

## 代码目录
### convert_scheduler.go
    主函数
### checkpoint
    实现了数据库增删改查的一些操作封装
### service
    服务


## 接口调用
### 创建转模任务/v1/convert
    POST /model/convert

### 查询转模记录/v1/convert/record/get
    POST /model/convert/record/get


## 负责人
    benshen;orlandochen

