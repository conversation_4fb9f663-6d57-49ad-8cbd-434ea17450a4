// Package cls ...
package cls

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	clssdk "github.com/tencentcloud/tencentcloud-cls-sdk-go"

	workerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/worker"
)

var clsClient *clssdk.AsyncProducerClient

// Initialize 初始化
func Initialize() {
	conf := workerconfig.GetConfig()
	producerConfig := clssdk.GetDefaultAsyncProducerClientConfig()
	producerConfig.Endpoint = conf.Cls.Endpoint
	producerConfig.AccessKeyID = conf.Cls.AccessKeyID
	producerConfig.AccessKeySecret = conf.Cls.AccessKeySecret
	producerInstance, err := clssdk.NewAsyncProducerClient(producerConfig)
	if err != nil {
		log.Fatalf("Init cls error: %s", err.Error())
	}
	clsClient = producerInstance
	clsClient.Start()
}

type Callback struct {
}

func (callback *Callback) Success(_ *clssdk.Result) {
}

func (callback *Callback) Fail(result *clssdk.Result) {
	log.Errorf("CLS Fail: Success=%v, Code=%v, Msg=%v, Attempts=%v, ReqID=%v, Time=%v",
		result.IsSuccessful(),
		result.GetErrorCode(),
		result.GetErrorMessage(),
		result.GetReservedAttempts(),
		result.GetRequestId(),
		result.GetTimeStampMs(),
	)
}

type ClsLog struct {
	TaskID             string
	LogLevel           string
	Content            string
	OriginModelName    string
	OriginModelVersion string
	OutputModelName    string
	ContainerName      string
}

func (c *ClsLog) ToMap() map[string]string {
	return map[string]string{
		"task_id":              c.TaskID,
		"log_level":            c.LogLevel,
		"content":              c.Content,
		"origin_model_name":    c.OriginModelName,
		"origin_model_version": c.OriginModelVersion,
		"output_model_name":    c.OutputModelName,
		"container_name":       c.ContainerName,
	}
}

func SendLog(clsLog *ClsLog) {
	conf := workerconfig.GetConfig()
	topicID := conf.Cls.TopicID

	tolog := clssdk.NewCLSLog(
		time.Now().Unix(),
		clsLog.ToMap(),
	)
	callBack := &Callback{}
	err := clsClient.SendLog(topicID, tolog, callBack)
	if err != nil {
		log.Errorf("Send log for task_id:%s failed:%s", clsLog.TaskID, err.Error())
	}
}
