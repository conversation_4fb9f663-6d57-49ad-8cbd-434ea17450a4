// Package convert 转模定义
package define

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	scdata "git.code.oa.com/RondaServing/ServingController/util/data"
)

// ConvertType 转模类型
type ConvertType string

const (
	Numerous2Trt ConvertType = "Numerous2Trt"
)

type EngineType string

const (
	EngineTrt     EngineType = "trt"
	EngineEvart   EngineType = "evart"
	EnginePytorch EngineType = "pytorch"
)

// ConvertStatus 转模状态
type ConvertStatus string

const (
	// TODO(orlandochen): 跟ServingController对齐
	// Waiting 待分配
	Waiting ConvertStatus = "waiting"
	// Running 处理中
	Running ConvertStatus = "running"
	// Failed 失败
	Failed ConvertStatus = "failed"
	// Success 成功
	Success ConvertStatus = "success"
)

func (c ConvertStatus) IsFinished() bool {
	return c == Success || c == Failed
}

// Value DB写入使用到的序列化函数
func (m NumerousConvertParam) Value() (driver.Value, error) {
	bytes, err := json.Marshal(m)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

// Scan DB读取使用到的反序列化函数
func (m *NumerousConvertParam) Scan(obj interface{}) error {
	bytes, err := scdata.GetBytes(obj)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, &m)
}

// NumerousConvertParam 无量转模
type NumerousConvertParam struct {
	// PredictTarget 预测目标,分号或逗号间隔，样例：pfb,lcr,y_pcr,y_real_click
	PredictTarget string `json:"predictTarget,omitempty" db:"predict_target"`
	// BatchSize batch size 分桶列表,分号或逗号间隔，样例：500,1000,1500
	BatchSize string `json:"batchSize,omitempty" db:"batch_size"`
	// CpuTarget CPU Target 的节点名,分号或逗号间隔，样例：concat_seq,concat_nn_stat
	CpuTarget string `json:"placeholderReplace,omitempty" db:"cpu_target"`
	// EngineType 引擎类型 trt/evart
	EngineType EngineType `json:"engine_type,omitempty" db:"engine_type"`
	// UseFp16 开启 FP16 量化
	UseFp16 bool `json:"fp16" db:"use_fp16"`
	// BuildRefittableEngine 开启 Refit
	BuildRefittableEngine bool `json:"use_refit" db:"build_refittable_engine"`
	// OpsetVersion 算子集版本
	OpsetVersion int `json:"opset_version,omitempty" db:"opset_version"`
	// UseEvartTool 使用evart工具生成trt engine
	UseEvartTool bool `json:"use_evart_tool" db:"use_evart_tool"`
	// UserInputs 指定user_inputs，支持自定义input维度
	UserInputs string `json:"user_inputs,omitempty" db:"user_inputs"`
	// ItemInputs 指定item_inputs，支持自定义input维度
	ItemInputs string `json:"item_inputs,omitempty" db:"item_inputs"`
	// ConvertWithRefit 转模过程中是否使用 refit 机制减少换摸时间，全量模型不走refit
	ConvertWithRefit bool `json:"convert_with_refit" db:"convert_with_refit"`
	// ForceConvertWithRefit 强制走refit，可替代ConvertWithRefit，且全量模型也走refit转模
	ForceConvertWithRefit bool `json:"force_convert_with_refit" db:"force_convert_with_refit"`
	// TrtVersion trt版本
	TrtVersion string `json:"trtVersion,omitempty" db:"trt_version"`
}

// ConvertParam 转模参数
type ConvertParam struct {
	// AppGroupID 应用组
	AppGroupID string `json:"appGroupID,omitempty" db:"app_group_id"`
	// ServerID 服务ID
	ServerID int `json:"serverID,omitempty" db:"server_id"`
	// Operator 转模负责人
	Operator string `json:"operator,omitempty" db:"operator"`
	// OriginModelName 原始模型名
	OriginModelName string `json:"originModelName,omitempty" db:"original_model_name"`
	// OriginModelVersion 原始模型版本
	OriginModelVersion string `json:"originModelVersion,omitempty" db:"original_model_version"`
	// OutputModelName 输出模型名
	OutputModelName string `json:"outputModelName,omitempty" db:"output_model_name"`
	// OutputModelVersion 输出模型版本
	OutputModelVersion string `json:"outputModelVersion,omitempty" db:"output_model_version"`
	// ConvertType 转模类型
	ConvertType ConvertType `json:"convertType,omitempty" db:"convert_type"`
	// GpuType gpu类型
	GpuType string `json:"gpuType,omitempty" db:"gpu_type"`
	// NumerousParam 无量转模参数
	NumerousParam NumerousConvertParam `json:"numerousParam" db:"numerous_param"`
}

// ConvertTask 转模任务
type ConvertTask struct {
	// TaskID 任务的ID
	TaskID string `json:"taskID" db:"task_id"`
	// Status 转模状态
	Status ConvertStatus `json:"status" db:"status"`
	// CreateTime 创建时间
	CreateTime time.Time `json:"createTime" db:"create_time"`
	// StartTime 开始时间
	StartTime time.Time `json:"startTime" db:"start_time"`
	// EndTime 结束时间
	EndTime time.Time `json:"endTime" db:"end_time"`
	// Deadline 截止时间
	Deadline time.Time `json:"deadline" db:"deadline"`
	// ConvertParam 转模参数
	ConvertParam `json:",inline" db:",inline"`
	// WorkerAssignment 任务分配的worker
	WorkerAssignment string `json:"workerAssignment" db:"worker_assignment"`
	// TaskResultMsg 转模结果详情
	TaskResultMsg TaskResultMsg `json:"taskResultMsg" db:"task_result_msg"`
	// TaskProcess 转模进度信息
	TaskProcess TaskProgress `json:"taskProcess" db:"task_process"`
	// Priority 任务优先级
	Priority uint `json:"priority" db:"priority"`
}

type ConvertTaskHistory struct {
	ConvertTask `json:",inline" db:",inline"`
}
