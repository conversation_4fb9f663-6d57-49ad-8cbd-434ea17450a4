package define

import (
	"database/sql/driver"
	"encoding/json"
	"time"

	scdata "git.code.oa.com/RondaServing/ServingController/util/data"
)

const (
	// TODO: 这里原来的拼写有误,升级后去掉
	UrlQueryTask   = "/convet/query_task"
	UrlSubmitTask  = "/convet/submit_task"
	UrlGetTasks    = "/convet/get_tasks"
	UrlReportTasks = "/convet/report_tasks"

	UrlNewQueryTask   = "/convert/query_task"
	UrlNewSubmitTask  = "/convert/submit_task"
	UrlNewGetTasks    = "/convert/get_tasks"
	UrlNewReportTasks = "/convert/report_tasks"
)

type WorkerBaseInfo struct {
	SetName       string `yaml:"setName" json:"setName" db:"set_name"`
	ContainerName string `yaml:"containerName" json:"containerName" db:"container_name"`
}

// --- query task ----
type QueryTaskReq struct {
	TaskID string `yaml:"taskID" json:"taskID" db:"task_id" validate:"required"`
}

type QueryTaskRsp struct {
	Response `yaml:",inline" json:",inline" db:",inline"`
	Data     ConvertTask `yaml:"data,omitempty" json:"data,omitempty" db:"data"`
}

// --- submit task ----
type SubmitTaskReq struct {
	TaskID       string `yaml:"taskID" json:"taskID" db:"task_id" validate:"required"`
	ConvertParam `yaml:",inline" json:",inline" db:"convert_param"`
}

type SubmitTaskRsp struct {
	Response `yaml:",inline" json:",inline" db:",inline"`
	Data     ConvertTask `yaml:"data,omitempty" json:"data,omitempty" db:"data"`
}

// --- get tasks ----
type GetTasksReq struct {
	WorkerBaseInfo `yaml:",inline" json:",inline" db:",inline"`
}

type GetTasksRsp struct {
	Response `yaml:",inline" json:",inline" db:",inline"`
	Data     []ConvertTask `yaml:"data,omitempty" json:"data,omitempty" db:"data"`
}

// ReportTask 上报任务状态
type ReportTask struct {
	TaskID       string       `yaml:"taskID" json:"taskID" db:"task_id"`
	TaskResult   TaskResult   `yaml:"taskResult" json:"taskResult" db:"task_result"`
	TaskProgress TaskProgress `yaml:"taskProgress" json:"taskProgress" db:"task_progress"`
}

// --- report tasks ----
type ReportTasksReq struct {
	Tasks []ReportTask `yaml:"tasks" json:"tasks" db:"tasks"`
}

// Value DB写入使用到的序列化函数
func (t TaskResultMsg) Value() (driver.Value, error) {
	bytes, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

// Scan DB读取使用到的反序列化函数
func (t *TaskResultMsg) Scan(obj interface{}) error {
	bytes, err := scdata.GetBytes(obj)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, &t)
}

type TaskResultMsg struct {
	ErrCode   string `yaml:"errCode,omitempty" json:"errCode,omitempty" db:"err_code"`
	ErrReason string `yaml:"errReason,omitempty" json:"errReason,omitempty" db:"err_reason"`
}

// TaskResult 转模结果
type TaskResult struct {
	// Status 转模状态
	Status ConvertStatus `yaml:"status,omitempty" json:"status,omitempty" db:"status"`
	// EndTime 结束时间
	EndTime time.Time `yaml:"endTime,omitempty" json:"endTime,omitempty" db:"end_time"`
	// TaskResultMsg 转模结果详情信息,如错误码等
	TaskResultMsg `yaml:",inline" json:",inline" db:",inline"`
}

// Value DB写入使用到的序列化函数
func (t TaskProgress) Value() (driver.Value, error) {
	bytes, err := json.Marshal(t)
	if err != nil {
		return nil, err
	}
	return string(bytes), nil
}

// Scan DB读取使用到的反序列化函数
func (t *TaskProgress) Scan(obj interface{}) error {
	bytes, err := scdata.GetBytes(obj)
	if err != nil {
		return err
	}
	return json.Unmarshal(bytes, &t)
}

// TaskProgress 转模进度
type TaskProgress struct {
	// TaskCount 需转模版本总数
	TaskCount int `yaml:"taskCount,omitempty" json:"taskCount,omitempty" db:"task_count"`
	// CurrentTaskNum 当前在进行第几个转模任务
	CurrentTaskNum int `yaml:"currentTaskNum,omitempty" json:"currentTaskNum,omitempty" db:"current_task_num"`
	// CurrentModelVersion 当前转模版本号
	CurrentModelVersion string `yaml:"currentModelVersion,omitempty" json:"currentModelVersion,omitempty" db:"current_model_version"`
}

type ReportTasksRsp struct {
	Response `yaml:",inline" json:",inline" db:",inline"`
}
