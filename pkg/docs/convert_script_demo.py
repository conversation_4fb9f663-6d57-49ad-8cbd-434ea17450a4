import json
import os
import sys

sys.path.append('./mock')

from plugin import BorePyTorchPlugin
from mock.sdk import JobParameter, ExecutorProvider


def convert(convert_param: dict):
    execution_provider = ExecutorProvider()
    plugin = BorePyTorchPlugin(execution_provider)
    [convert_param.setdefault(k, v) for k, v in
     [('f_app_group_id', '874'),
      ('f_session_id', '-1'),
      ('appinstance_name', '-1'),
      ('opset_version', 13),
      ('boreConfig.resourceGroupName', '-1'),
      ('f_job_id', '-1'),
      ('job_owner', 'benshen'),
      ('op_version', '1.latest'),
      ('env', 'formal'),
      ('is_debug', 'False'),
      ('modelName', 'NumerousTensorRT'),
      ('modelVersion', '2024_10_17_001138'),
      ('outputModelName', 'NumerousTensorRT_trt'),
      ('is_register', False),
      ('keepVersion', '5'),
      ('predictTarget', ''),
      ('placeholderReplace', ''),
      ('batchSize', '50'),
      ('engine_type', 'trt'),# trt/evart
      ('fp16', False),
      ('use_refit', True),
      ('paramsPairs', '')]]
    job_parameter = JobParameter(convert_param)
    job_parameter.secret = {
        'venus_openapi_sid': convert_param['proxy_user_secret_id'],
        'venus_openapi_skey': convert_param['proxy_user_secret_key'],
    }
    plugin.execute(job_parameter)


if __name__ == '__main__':
    # 在 https://venus.woa.com/#/openapi/accountManage/personalAccount 可以申请查看 secret_id 和 secret_key
    input_data = sys.stdin.read()
    convert_param = json.loads(input_data)
    print(convert_param)
    # 创建目录
    directory = '/usr/local/app/convert/tasks/status'
    os.makedirs(directory, exist_ok=True)
    # 创建文件
    file_path = f'{directory}/{convert_param["taskID"]}.json'
    try:
        convert(convert_param)
    except Exception as e:
        with open(file_path, 'w') as file:
            file.write('{"status": "failed"}')
        raise e
    with open(file_path, 'w') as file:
        file.write('{"status": "success"}')

