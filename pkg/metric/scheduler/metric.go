package scheduler

import (
	"strconv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/metrics"
	"github.com/samber/lo"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

var (
	ConvertTaskWaitingNum = metrics.Gauge("scheduler.TaskWaitingNum")
	ConvertTaskRunningnum = metrics.Gauge("scheduler.TaskRunningNum")
)

func Initialize() {
	go startReportMetric()
}

func startReportMetric() {
	const interval = 15 * time.Second
	ticker := time.NewTicker(interval)
	defer ticker.Stop()
	for range ticker.C {
		doReportMetric()
	}
}

func doReportMetric() {
	var waitingNum, runningNum float64
	tasks := schedulertask.GetConvertTasksByFun(func(task *define.ConvertTask) bool {
		return task.Status == define.Waiting || task.Status == define.Running
	})
	for _, task := range tasks {
		if task.Status == define.Waiting {
			waitingNum++
		} else {
			runningNum++
		}
	}
	ConvertTaskWaitingNum.Set(waitingNum)
	ConvertTaskRunningnum.Set(runningNum)
}

func ReportConvertTask(task *define.ConvertTask) {
	var duration float64
	if !task.EndTime.IsZero() && !task.StartTime.IsZero() {
		duration = task.EndTime.Sub(task.StartTime).Seconds()
	}
	success := lo.Ternary(task.Status == define.Success, 1.0, 0.0)
	failed := lo.Ternary(task.Status != define.Success, 1.0, 0.0)
	metrics.ReportMultiDimensionMetricsX(
		"convert_task",
		[]*metrics.Dimension{
			{Name: "OriginModelName", Value: task.OriginModelName},
			{Name: "OriginModelVersion", Value: task.OriginModelVersion},
			{Name: "OutputmodelName", Value: task.OutputModelName},
			{Name: "OutputmodelVersion", Value: task.OutputModelVersion},
			{Name: "ServerID", Value: strconv.Itoa(task.ServerID)},
			{Name: "AppGroupID", Value: task.AppGroupID},
			{Name: "GpuType", Value: task.GpuType},
			{Name: "Status", Value: string(task.Status)},
			{Name: "ErrCode", Value: task.TaskResultMsg.ErrCode},
		},
		[]*metrics.Metrics{
			metrics.NewMetrics("total", 1, metrics.PolicySUM),
			metrics.NewMetrics("success", success, metrics.PolicySUM),
			metrics.NewMetrics("failed", failed, metrics.PolicySUM),
			metrics.NewMetrics("duration", duration, metrics.PolicyAVG),
		},
	)
}
