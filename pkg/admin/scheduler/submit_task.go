package scheduler

import (
	"fmt"
	"net/http"
	"time"

	scuuid "git.code.oa.com/RondaServing/ServingController/util/uuid"
	trpcadmin "git.code.oa.com/trpc-go/trpc-go/admin"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/admin"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

func init() {
	trpcadmin.HandleFunc("/task/submit_task", cmdSubmitTask)
}

func cmdSubmitTask(w http.ResponseWriter, r *http.Request) {
	if !admin.ReqPostInit(w, r) {
		return
	}
	convertParam, err := admin.GetTaskConvertParam(r)
	if err != nil {
		admin.RetResponse(w, -1, err.Error())
		return
	}

	convertTask := define.ConvertTask{
		TaskID:       scuuid.GetUUID(),
		ConvertParam: convertParam,
		Status:       define.Waiting,
		CreateTime:   time.Now(),
		StartTime:    time.Time{},
		Deadline:     time.Now().Add(time.Hour),
	}
	if err := schedulertask.ReplaceConvertTask(convertTask); err != nil {
		admin.RetResponse(w, -1, fmt.Sprintf("replace convert task failed, err: %v", err))
		return
	}
	admin.RetResponse(w, 0, "ok")
}
