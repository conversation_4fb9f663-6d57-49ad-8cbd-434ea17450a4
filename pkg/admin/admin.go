package admin

import (
	"encoding/json"
	"fmt"
	"net/http"

	"github.com/samber/lo"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

func RetResponse(w http.ResponseWriter, ret int, message string) {
	rsp := define.Response{RetCode: ret, Message: message}
	json.NewEncoder(w).Encode(rsp)
}

func ReqPostInit(w http.ResponseWriter, r *http.Request) bool {
	w.Header().Set("X-Content-Type-Options", "nosniff")
	w.Header().Set("Content-Type", "application/json; charset=utf-8")
	if r.Method != http.MethodPost {
		RetResponse(w, -1, "Not POST method")
		return false
	}
	if err := r.ParseForm(); err != nil {
		RetResponse(w, -1, err.Error())
		return false
	}
	return true
}

func GetTaskConvertParam(r *http.Request) (define.ConvertParam, error) {
	appGroupID := r.Form.Get("appGroupID")
	if appGroupID == "" {
		return define.ConvertParam{}, fmt.Errorf("appGroupID is nil")
	}
	operator := r.Form.Get("operator")
	if operator == "" {
		return define.ConvertParam{}, fmt.Errorf("operator is nil")
	}
	originModelName := r.Form.Get("originModelName")
	originModelVersion := r.Form.Get("originModelVersion")
	if originModelName == "" || originModelVersion == "" {
		return define.ConvertParam{}, fmt.Errorf("originModelName or originModelVersion is nil")
	}
	outputModelName := r.Form.Get("outputModelName")
	if outputModelName == "" {
		return define.ConvertParam{}, fmt.Errorf("outputModelName is nil")
	}
	outputModelVersion := r.Form.Get("outputModelVersion")
	batchSize := r.Form.Get("batchSize")
	if batchSize == "" {
		return define.ConvertParam{}, fmt.Errorf("batchSize is nil")
	}
	predictTarget := r.Form.Get("predictTarget")
	if predictTarget == "" {
		return define.ConvertParam{}, fmt.Errorf("predictTarget is nil")
	}
	gpuType := r.Form.Get("gpuType")
	gpuType = lo.Ternary(gpuType == "", "A10", gpuType)
	cpuTarget := r.Form.Get("cpuTarget")
	engineType := r.Form.Get("engineType")
	engineType = lo.Ternary(engineType == "", string(define.EngineTrt), engineType)
	useFp16 := false
	useFp16 = lo.Ternary(r.Form.Get("useFp16") == "true", true, false)
	buildRefittableEngine := true
	buildRefittableEngine = lo.Ternary(r.Form.Get("buildRefittableEngine") == "false", false, true)
	useEvartTool := false
	useEvartTool = lo.Ternary(r.Form.Get("useEvartTool") == "true", true, false)
	userInputs := r.Form.Get("userInputs")
	itemInputs := r.Form.Get("itemInputs")
	convertWithRefit := false
	convertWithRefit = lo.Ternary(r.Form.Get("convertWithRefit") == "true", true, false)
	forceConvertWithRefit := false
	forceConvertWithRefit = lo.Ternary(r.Form.Get("forceConvertWithRefit") == "true", true, false)

	convertParam := define.ConvertParam{
		AppGroupID:         appGroupID,
		Operator:           operator,
		OriginModelName:    originModelName,
		OriginModelVersion: originModelVersion,
		OutputModelName:    outputModelName,
		OutputModelVersion: lo.Ternary(outputModelVersion == "", originModelVersion, outputModelVersion),
		ConvertType:        define.Numerous2Trt,
		GpuType:            gpuType,
		NumerousParam: define.NumerousConvertParam{
			PredictTarget:         predictTarget,
			BatchSize:             batchSize,
			CpuTarget:             cpuTarget,
			EngineType:            define.EngineType(engineType),
			UseFp16:               useFp16,
			BuildRefittableEngine: buildRefittableEngine,
			OpsetVersion:          13,
			UseEvartTool:          useEvartTool,
			UserInputs:            userInputs,
			ItemInputs:            itemInputs,
			ConvertWithRefit:      convertWithRefit,
			ForceConvertWithRefit: forceConvertWithRefit,
			TrtVersion:            "",
		},
	}

	return convertParam, nil
}
