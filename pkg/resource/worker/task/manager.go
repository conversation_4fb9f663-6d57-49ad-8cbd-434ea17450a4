package task

import (
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

var (
	mutex    sync.Mutex
	allTasks tasksType
)

// Initialize 初始化
func Initialize() {
	log.Info("Init worker task")
	initManager()
}

func initManager() {
	allTasks = tasksType{
		all: make(allTaskType),
	}
}

type tasksType struct {
	all allTaskType
}

type allTaskType map[string]define.ReportTask

func (r *allTaskType) replace(task define.ReportTask) {
	(*r)[task.TaskID] = task
}

func (r *allTaskType) delete(taskID string) {
	if _, ok := (*r)[taskID]; !ok {
		return
	}
	delete(*r, taskID)
}

func (r *allTaskType) get(taskID string) (define.ReportTask, bool) {
	task, ok := (*r)[taskID]
	return task, ok
}

func (r *allTaskType) len() int {
	return len(*r)
}

func (r *allTaskType) getAllTaskIDs() []string {
	taskIDs := make([]string, 0, len(allTasks.all))
	for taskID := range allTasks.all {
		taskIDs = append(taskIDs, taskID)
	}
	return taskIDs
}

func (r *allTaskType) getAllTasks() []define.ReportTask {
	rets := make([]define.ReportTask, 0, r.len())
	for _, task := range *r {
		rets = append(rets, task)
	}
	return rets
}

func ReplaceReportTask(task define.ReportTask) {
	mutex.Lock()
	defer mutex.Unlock()
	allTasks.all.replace(task)
}

func DeleteReportTask(taskID string) {
	mutex.Lock()
	defer mutex.Unlock()
	allTasks.all.delete(taskID)
}

func GetAllReportTasks() []define.ReportTask {
	mutex.Lock()
	defer mutex.Unlock()
	return allTasks.all.getAllTasks()
}
