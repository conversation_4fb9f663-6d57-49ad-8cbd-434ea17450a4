// Package instance worker实例的缓存和数据库操作
package instance

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/checkpoint"
)

const tableName = "t_worker_instance"

func initDatabase() {
	if err := createTable(); err != nil {
		log.Fatalf("Create table %s error:%s", tableName, err.Error())
	}
	if err := loadFromDatabase(); err != nil {
		log.Fatalf("Load worker instance error:%s", err.Error())
	}
	log.Infof("Load worker instance succ, size:%d", getWorkerInstsLen())
}

func createTable() error {
	// 执行 TEST 的时候使用了sqlite才去创建表,实际运行mysql的时候应该先创建好
	if checkpoint.GetDBDriverName() == checkpoint.DriverMySQL {
		return nil
	}
	sql := `create table if not exists %s (
    	container_name varchar(128),
    	set_name varchar(128),
        status varchar(64),
    	last_heartbeat datetime)`
	_, err := checkpoint.GetDBHandler().Exec(fmt.Sprintf(sql, tableName))
	return err
}

func loadFromDatabase() error {
	rows, err := checkpoint.StructSelect(
		checkpoint.GetDBHandler(), tableName, &WorkerInstance{}, nil, nil)
	if err != nil {
		return err
	}
	defer rows.Close()
	for rows.Next() {
		var inst WorkerInstance
		if err = rows.StructScan(&inst); err != nil {
			return err
		}
		replaceWorkerInstInCache(inst)
	}
	return nil
}

func replaceWorkerInstInDatabase(workerInst WorkerInstance) error {
	_, err := checkpoint.StructReplace(checkpoint.GetDBHandler(), tableName, workerInst)
	return err
}

func deleteWorkerInstInDatabase(containerName string) error {
	conditions := map[string]interface{}{"container_name": containerName}
	_, err := checkpoint.SQLDelete(checkpoint.GetDBHandler(), tableName, conditions)
	return err
}
