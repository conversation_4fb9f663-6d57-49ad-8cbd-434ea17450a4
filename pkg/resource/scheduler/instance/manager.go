// Package instance worker实例的缓存和数据库操作
package instance

import (
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
)

var (
	mutex          sync.Mutex
	allWorkerInsts workerInstsType
)

func initManager() {
	allWorkerInsts = workerInstsType{
		all:   make(allWorkerInstType),
		bySet: make(workerInstBySet),
	}
}

type workerInstsType struct {
	all   allWorkerInstType
	bySet workerInstBySet
}

// --- all worker ---
// OutputModelKey -> WorkerInstance
type allWorkerInstType map[string]WorkerInstance

func (w *allWorkerInstType) replace(inst WorkerInstance) {
	(*w)[inst.ContainerName] = inst
}

func (w *allWorkerInstType) delete(containerName string) {
	if _, ok := (*w)[containerName]; !ok {
		return
	}
	delete(*w, containerName)
}

func (w *allWorkerInstType) get(containerName string) (WorkerInstance, bool) {
	Task, ok := (*w)[containerName]
	return Task, ok
}

func (w *allWorkerInstType) len() int {
	return len((*w))
}

func (w *allWorkerInstType) getAll() []WorkerInstance {
	rets := make([]WorkerInstance, 0, w.len())
	for _, inst := range *w {
		rets = append(rets, inst)
	}
	return rets
}

// --- worker by set ----
// SetName -> ContainName
type workerInstBySet map[string]map[string]struct{}

func (w *workerInstBySet) add(inst WorkerInstance) {
	setName := inst.SetName
	containerName := inst.ContainerName
	if _, ok := (*w)[setName]; !ok {
		(*w)[setName] = make(map[string]struct{})
	}
	(*w)[setName][containerName] = struct{}{}
}

func (w *workerInstBySet) delete(inst WorkerInstance) {
	setName := inst.SetName
	containerName := inst.ContainerName
	if modelMap, ok := (*w)[setName]; ok {
		delete(modelMap, containerName)
		if len(modelMap) == 0 {
			delete(*w, setName)
		}
	}
}

func (w *workerInstBySet) get(setName string) map[string]struct{} {
	return (*w)[setName]
}

func getWorkerInstsLen() int {
	mutex.Lock()
	defer mutex.Unlock()
	return allWorkerInsts.all.len()
}

func replaceWorkerInstInCache(inst WorkerInstance) {
	mutex.Lock()
	defer mutex.Unlock()
	allWorkerInsts.all.replace(inst)
	allWorkerInsts.bySet.add(inst)
}

func deleteWorkerInstInCache(inst WorkerInstance) {
	mutex.Lock()
	defer mutex.Unlock()
	allWorkerInsts.all.delete(inst.ContainerName)
	allWorkerInsts.bySet.delete(inst)
}

// ReplaceWorkerInst 增加/更新Worker实例
func ReplaceWorkerInst(inst WorkerInstance) error {
	if err := replaceWorkerInstInDatabase(inst); err != nil {
		log.Errorf("Replace inst:%+v failed:%s", inst, err.Error())
		return err
	}
	replaceWorkerInstInCache(inst)
	return nil
}

// DeleteWorkerInst 删除Worker实例
func DeleteWorkerInst(inst WorkerInstance) error {
	if err := deleteWorkerInstInDatabase(inst.ContainerName); err != nil {
		log.Errorf("Delete WorkerInstance:%+v failed:%s", inst, err.Error())
		return err
	}
	deleteWorkerInstInCache(inst)
	return nil
}

// GetWorkerInst 返回指定worker实例
func GetWorkerInst(containerName string) (WorkerInstance, bool) {
	mutex.Lock()
	defer mutex.Unlock()
	return allWorkerInsts.all.get(containerName)
}

// GetWorkerInstsBySet 返回指定Set下的所有worker实例
func GetWorkerInstsBySet(setName string) map[string]WorkerInstance {
	mutex.Lock()
	defer mutex.Unlock()
	ret := make(map[string]WorkerInstance)
	workerInsts := allWorkerInsts.bySet.get(setName)
	for containerName := range workerInsts {
		if inst, ok := allWorkerInsts.all.get(containerName); ok {
			ret[inst.ContainerName] = inst
		}
	}
	return ret
}

func GetAllWorkerInsts() []WorkerInstance {
	mutex.Lock()
	defer mutex.Unlock()
	return allWorkerInsts.all.getAll()
}

func GetWorkerInstsWithCB(cb func(inst *WorkerInstance) bool, enableLimit bool, limitNum int) []WorkerInstance {
	ret := make([]WorkerInstance, 0, 1024)
	mutex.Lock()
	defer mutex.Unlock()
	for _, instance := range allWorkerInsts.all.getAll() {
		if cb(&instance) {
			ret = append(ret, instance)
			if enableLimit {
				limitNum--
				if limitNum <= 0 {
					break
				}
			}
		}
	}
	return ret
}
