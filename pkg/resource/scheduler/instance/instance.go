// Package instance worker实例的缓存和数据库操作
package instance

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

// Initialize 初始化
func Initialize() {
	log.Info("Init worker instance")
	initManager()
	initDatabase()
}

// WorkerInstance worker实例
type WorkerInstance struct {
	define.WorkerBaseInfo `json:",inline" yaml:",inline" db:",inline"`
	// Status worker状态
	Status define.WorkerStatus `json:"status" yaml:"status" db:"status"`
	// Healthy 健康状态
	Healthy bool `json:"healthy" yaml:"healthy" db:"healthy"`
	// LastHeartbeat 最后心跳时间
	LastHeartbeat time.Time `json:"lastHeartbeat" yaml:"lastHeartbeat" db:"last_heartbeat"`
}
