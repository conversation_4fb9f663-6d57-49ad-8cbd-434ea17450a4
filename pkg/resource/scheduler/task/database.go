// Package task 转模任务的缓存和数据库操作
package task

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/checkpoint"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

const tableName = "t_convert_task"

func initDatabase() {
	if err := createTable(); err != nil {
		log.Fatalf("Create table %s error:%s", tableName, err.Error())
	}
	if err := loadFromDatabase(); err != nil {
		log.Fatalf("Load convert task error:%s", err.Error())
	}
	log.Infof("Load convert task succ, all:%d, running:%d", getAllTasksNum(), getRunningTasksNum())
}

func createTable() error {
	// 执行 TEST 的时候使用了sqlite才去创建表,实际运行mysql的时候应该先创建好
	if checkpoint.GetDBDriverName() == checkpoint.DriverMySQL {
		return nil
	}
	sql := `create table if not exists %s (
    	task_id varchar(64),
    	original_model_name varchar(128),
    	original_model_version varchar(128),
        output_model_name varchar(128),
        output_model_version varchar(128),
        output_group_id int(11),
	    convert_type varchar(64),
        param json,
        runtime_data json,
        status varchar(64),
        start_time datetime,
        end_time datetime,
    	deadline datetime,
        primary key (id))`
	_, err := checkpoint.GetDBHandler().Exec(fmt.Sprintf(sql, tableName))
	return err
}

func loadFromDatabase() error {
	rows, err := checkpoint.StructSelect(checkpoint.GetDBHandler(), tableName, &define.ConvertTask{}, nil, nil)
	if err != nil {
		return err
	}
	defer rows.Close()
	for rows.Next() {
		var task define.ConvertTask
		if err = rows.StructScan(&task); err != nil {
			return err
		}
		replaceConvertTaskInCache(task)
	}
	return nil
}

func replaceConvertTaskInDatabase(task *define.ConvertTask) error {
	_, err := checkpoint.StructReplace(checkpoint.GetDBHandler(), tableName, *task)
	return err
}

func deleteConvertTaskInDatabase(taskID string) error {
	conditions := map[string]interface{}{"task_id": taskID}
	_, err := checkpoint.SQLDelete(checkpoint.GetDBHandler(), tableName, conditions)
	return err
}
