// Package task 转模任务的缓存和数据库操作
package task

import (
	"sync"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/samber/lo"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

var (
	mutex    sync.Mutex
	allTasks tasksType
)

func initManager() {
	allTasks = tasksType{
		all:                make(allTaskType),
		byRunning:          make(runningTasksMap),
		byOutputModel:      make(taskByOutputModel),
		byWorkerAssignment: make(taskByWorkerAssignment),
	}
}

type tasksType struct {
	all                allTaskType
	byRunning          runningTasksMap
	byOutputModel      taskByOutputModel
	byWorkerAssignment taskByWorkerAssignment
}

// --- all task ----
// ModelLabel -> ConvertTask
type allTaskType map[string]define.ConvertTask

func (r *allTaskType) replace(task define.ConvertTask) {
	(*r)[task.TaskID] = task
}

func (r *allTaskType) delete(taskID string) {
	if _, ok := (*r)[taskID]; !ok {
		return
	}
	delete(*r, taskID)
}

func (r *allTaskType) get(taskID string) (define.ConvertTask, bool) {
	task, ok := (*r)[taskID]
	return task, ok
}

func (r *allTaskType) len() int {
	return len(*r)
}

func (r *allTaskType) getAllTaskIDs() []string {
	taskIDs := make([]string, 0, len(allTasks.all))
	for taskID := range allTasks.all {
		taskIDs = append(taskIDs, taskID)
	}
	return taskIDs
}

func (r *allTaskType) getAllTasks() []define.ConvertTask {
	rets := make([]define.ConvertTask, 0, r.len())
	for _, task := range *r {
		rets = append(rets, task)
	}
	return rets
}

// ---- running task ----
// runningTasksMap 正在运行的任务
type runningTasksMap map[string]struct{}

// replace 增加/更新running task
func (r *runningTasksMap) replace(taskID string) {
	(*r)[taskID] = struct{}{}
}

// delete 删除running task
func (r *runningTasksMap) delete(taskID string) {
	delete(*r, taskID)
}

func (r *runningTasksMap) len() int {
	return len(*r)
}

func (r *runningTasksMap) exists(taskID string) bool {
	_, ok := (*r)[taskID]
	return ok
}

func (r *runningTasksMap) getAll() []string {
	return lo.Keys(*r)
}

// --- task by output model ----
// taskByOutputModel 按导出模型组织,ModelLabel -> list
type taskByOutputModel map[string]map[string]struct{}

func (t *taskByOutputModel) replace(modelName string, taskID string) {
	taskIDMap, ok := (*t)[modelName]
	if !ok {
		taskIDMap = make(map[string]struct{})
		(*t)[modelName] = taskIDMap
	}
	taskIDMap[taskID] = struct{}{}
}

func (t *taskByOutputModel) delete(modelName string, taskID string) {
	taskIDMap, ok := (*t)[modelName]
	if !ok {
		return
	}
	delete(taskIDMap, taskID)
	if len(taskIDMap) == 0 {
		delete(*t, modelName)
	}
}

func (t *taskByOutputModel) getTaskIDs(modelName string) []string {
	taskIDMap, ok := (*t)[modelName]
	if !ok {
		return nil
	}

	var ret []string
	for taskID := range taskIDMap {
		ret = append(ret, taskID)
	}
	return ret
}

// --- task by worker assignment ----
type taskByWorkerAssignment map[string]map[string]struct{}

func (t *taskByWorkerAssignment) replace(taskID string, containerName string) {
	taskIDMap, ok := (*t)[containerName]
	if !ok {
		taskIDMap = make(map[string]struct{})
		(*t)[containerName] = taskIDMap
	}
	taskIDMap[taskID] = struct{}{}
}

func (t *taskByWorkerAssignment) delete(taskID string, containerName string) {
	taskIDMap, ok := (*t)[containerName]
	if !ok {
		return
	}
	delete(taskIDMap, taskID)
	if len(taskIDMap) == 0 {
		delete(*t, containerName)
	}
}

func (t *taskByWorkerAssignment) getTaskIDs(containerName string) []string {
	taskIDMap, ok := (*t)[containerName]
	if !ok {
		return nil
	}

	var ret []string
	for taskID := range taskIDMap {
		ret = append(ret, taskID)
	}
	return ret
}

func getAllTasksNum() int {
	mutex.Lock()
	defer mutex.Unlock()
	return len(allTasks.all)
}

func getRunningTasksNum() int {
	mutex.Lock()
	defer mutex.Unlock()
	return allTasks.byRunning.len()
}

func replaceConvertTaskInCache(newTask define.ConvertTask) {
	mutex.Lock()
	defer mutex.Unlock()
	oldAssignment := ""
	if oldTask, ok := allTasks.all.get(newTask.TaskID); ok {
		oldAssignment = oldTask.WorkerAssignment
	}

	switch newTask.Status {
	case define.Running:
		allTasks.byRunning.replace(newTask.TaskID)
		allTasks.byWorkerAssignment.replace(newTask.TaskID, newTask.WorkerAssignment)
	default:
		newTask.WorkerAssignment = ""
		allTasks.byRunning.delete(newTask.TaskID)
	}
	allTasks.all.replace(newTask)
	allTasks.byOutputModel.replace(newTask.OutputModelName, newTask.TaskID)
	if oldAssignment != "" && oldAssignment != newTask.WorkerAssignment {
		allTasks.byWorkerAssignment.delete(newTask.TaskID, oldAssignment)
	}
}

func deleteConvertTaskInCache(taskID string) {
	mutex.Lock()
	defer mutex.Unlock()
	task, ok := allTasks.all.get(taskID)
	if !ok {
		return
	}
	allTasks.all.delete(taskID)
	allTasks.byRunning.delete(taskID)
	allTasks.byOutputModel.delete(task.OutputModelName, task.TaskID)
	allTasks.byWorkerAssignment.delete(taskID, task.WorkerAssignment)
}

// ReplaceConvertTask 增加/更新ConvertTask
func ReplaceConvertTask(task define.ConvertTask) error {
	if err := replaceConvertTaskInDatabase(&task); err != nil {
		log.Errorf("Replace convert task:%+v failed:%s", task, err.Error())
		return err
	}
	replaceConvertTaskInCache(task)
	return nil
}

// DeleteConvertTask 删除ConvertTask
func DeleteConvertTask(taskID string) error {
	if err := deleteConvertTaskInDatabase(taskID); err != nil {
		log.Errorf("Delete Convert Task:%+v failed:%s", taskID, err.Error())
		return err
	}
	deleteConvertTaskInCache(taskID)
	return nil
}

// GetConvertTaskByID 返回指定Task
func GetConvertTaskByID(taskID string) (define.ConvertTask, bool) {
	mutex.Lock()
	defer mutex.Unlock()
	return allTasks.all.get(taskID)
}

func GetConvertTasksByFun(fun func(task *define.ConvertTask) bool) []define.ConvertTask {
	mutex.Lock()
	defer mutex.Unlock()
	ret := make([]define.ConvertTask, 0, allTasks.all.len())
	for _, task := range allTasks.all {
		if fun(&task) {
			ret = append(ret, task)
		}
	}
	return ret
}

func GetAssignmentTasksByWorker(containerName string) []define.ConvertTask {
	ret := make([]define.ConvertTask, 0, 16)
	mutex.Lock()
	defer mutex.Unlock()
	taskIDs := allTasks.byWorkerAssignment.getTaskIDs(containerName)
	for _, taskID := range taskIDs {
		ret = append(ret, allTasks.all[taskID])
	}
	return ret
}

// GetOutputModelNameRunningTasks 返回输出模型名运行中的任务(TaskID)
func GetOutputModelNameRunningTasks(outputModelName string) []string {
	ret := make([]string, 0, 16)
	mutex.Lock()
	defer mutex.Unlock()
	taskIDs := allTasks.byOutputModel.getTaskIDs(outputModelName)
	for _, taskID := range taskIDs {
		task, ok := allTasks.all.get(taskID)
		if !ok {
			continue
		}
		if task.Status == define.Running {
			ret = append(ret, taskID)
		}
	}
	return ret
}
