// Package taskhistory 历史转模任务,用于计费和追溯
package taskhisory

import (
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/checkpoint"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/util/datetime"
)

const tableNamePrefix = "t_convert_task_history"

// Initialize 初始化
func Initialize() {
	log.Info("Init task history")
	initDatabase()
}

func initDatabase() {
	tableName := fmt.Sprintf("%s_%s", tableNamePrefix, datetime.GetYearMonthByTime(time.Now()))
	if err := createTable(tableName); err != nil {
		log.Fatalf("Create table %s error:%s", tableName, err.Error())
	}
}

func createTable(tableName string) error {
	// 执行 TEST 的时候使用了sqlite才去创建表,实际运行mysql的时候应该先创建好
	if checkpoint.GetDBDriverName() == checkpoint.DriverMySQL {
		return nil
	}
	sql := `create table if not exists %s (
    	task_id varchar(64),
    	original_model_name varchar(128),
    	original_model_version varchar(128),
        output_model_name varchar(128),
        output_model_version varchar(128),
        output_group_id int(11),
	    convert_type varchar(64),
        param json,
        runtime_data json,
        status varchar(64),
        start_time datetime,
        end_time datetime,
    	deadline datetime,
        primary key (id))`
	_, err := checkpoint.GetDBHandler().Exec(fmt.Sprintf(sql, tableName))
	return err
}

func replaceConvertTaskHistoryInDatabase(tableName string, item *define.ConvertTaskHistory) error {
	_, err := checkpoint.StructReplace(checkpoint.GetDBHandler(), tableName, *item)
	return err
}

func ReplaceConvertTaskHistory(item *define.ConvertTaskHistory) error {
	endTime := item.ConvertTask.EndTime
	tableName := fmt.Sprintf("%s_%s", tableNamePrefix, datetime.GetYearMonthByTime(endTime))
	return replaceConvertTaskHistoryInDatabase(tableName, item)
}
