// Package etcd 定义了依赖etcd实现服务主备逻辑
package etcd

import (
	"context"
	"fmt"
	"strings"
	"time"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
	clientv3 "go.etcd.io/etcd/client/v3"
	"go.etcd.io/etcd/client/v3/concurrency"
)

// ETCD etcd 连接实例定义
type ETCD struct {
	client           *clientv3.Client
	keepAliveInteval int64
	lockKeyPrefix    string
}

// NewEtcd 创建etcd客户端
func NewEtcd(user, passwd, endpoints, keyPrefix string, interval int64) (*ETCD, error) {
	cfg := clientv3.Config{
		Endpoints:   strings.Split(endpoints, ","),
		DialTimeout: time.Second * time.Duration(interval),
		Username:    user,
		Password:    passwd,
	}
	client, err := clientv3.New(cfg)
	if err != nil {
		log.Errorf("connect etcd failed, err=%s", err.Error())
		return nil, err
	}
	etcd := &ETCD{
		client:           client,
		lockKeyPrefix:    keyPrefix,
		keepAliveInteval: interval,
	}
	fmt.Println("New etcd success")
	return etcd, nil
}

// Campaign 抢锁
func (e *ETCD) Campaign(parentCtx context.Context) error {
	// 当外层的context关闭时，我们也会优雅的退出。
	ctx, _ := context.WithCancel(parentCtx)
	// 创建session，session参与选主，etcd的client需要自己传入。
	// session中keepAlive机制会一直续租，如果keepAlive断掉，session.Done会收到退出信号。
	s, err := concurrency.NewSession(e.client, concurrency.WithTTL(int(e.keepAliveInteval)))
	if err != nil {
		log.Errorf("new etcd session failed, err=%s", err.Error())
		return err
	}
	// 创建一个新的etcd选举election
	key := e.lockKeyPrefix + trpc.GlobalConfig().Global.FullSetName
	log.Infof("Etcd lock key=" + key)
	elect := concurrency.NewElection(s, key)
	// 调用Campaign方法，成为leader的节点会运行出来，非leader节点会阻塞在里面。
	if err = elect.Campaign(ctx, trpc.GlobalConfig().Global.LocalIP); err != nil {
		s.Close()
		log.Errorf("Campaign error, err:%s", err.Error())
		return err
	}
	go func() {
		defer func() {
			ctxTmp, _ := context.WithTimeout(context.Background(), time.Second*1)
			elect.Resign(ctxTmp)
			s.Close()
		}()
		// 运行到这的协程，成为leader，分布式下只有一个。
		fmt.Println("campaign", "success", "ip", trpc.GlobalConfig().Global.LocalIP)
		select {
		case <-s.Done(): // 如果因为网络因素导致与etcd断开了keepAlive，这里需要退出程序
			log.Fatal("campaign, session has done")
		case <-ctx.Done():
			log.Infof("exit")
			return
		}
	}()
	return nil
}
