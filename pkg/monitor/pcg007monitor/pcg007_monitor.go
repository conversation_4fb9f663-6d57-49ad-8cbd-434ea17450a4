// Package pcg007monitor 实现pcg 007监控告警相关操作
package pcg007monitor

import (
	"errors"
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/metrics"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/monitor/monreporter"
)

// PCG007Monitor PCG 007监控,实现了MonitorReporter接口
type PCG007Monitor struct {
}

// NewPCG007Monitor PCG007Monitor构造函数
func NewPCG007Monitor() *PCG007Monitor {
	return &PCG007Monitor{}
}

// MonitorReport 上报监控告警
func (m *PCG007Monitor) MonitorReport(obj interface{}) error {
	switch v := obj.(type) {
	case monreporter.CustomWarning:
		return m.ReportCustomWarning(v)
	default:
		return errors.New("obj unknown")
	}
}

// ReportCustomWarning 上报自定义告警
func (m *PCG007Monitor) ReportCustomWarning(warn monreporter.CustomWarning) error {
	switch warn.Level {
	case monreporter.Notify, monreporter.Warn:
	case monreporter.Alert, monreporter.Urgent:
	default:
		return fmt.Errorf("warn level:%s error", warn.Level)
	}
	// title/content 需要去 007 配置显示名
	var dimens []*metrics.Dimension
	dimens = append(dimens, &metrics.Dimension{Name: "title", Value: warn.Title})
	dimens = append(dimens, &metrics.Dimension{Name: "content", Value: warn.Content})
	var values []*metrics.Metrics
	values = append(values, metrics.NewMetrics("count", 1, metrics.PolicySUM))
	record := metrics.NewMultiDimensionMetricsX(string(warn.Level), dimens, values)
	return metrics.Report(record)
}
