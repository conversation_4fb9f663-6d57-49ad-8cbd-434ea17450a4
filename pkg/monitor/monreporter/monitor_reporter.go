package monreporter

// MonitorReporter 上报监控告警接口
type MonitorReporter interface {
	MonitorReport(interface{}) error
}

// CustomWarnLevel 自定义告警级别
type CustomWarnLevel string

const (
	// Notify 通知
	Notify CustomWarnLevel = "notify"
	// Warn 提醒
	Warn CustomWarnLevel = "warn"
	// Alert 警惕
	Alert CustomWarnLevel = "alert"
	// Urgent 紧急
	Urgent CustomWarnLevel = "urgent"
)

// CustomWarning 模块自定义的告警,一般是内部错误,用于提醒开发和运维
type CustomWarning struct {
	Level   CustomWarnLevel
	Title   string
	Content string
}
