// Package monitor 实现监控告警相关操作
package monitor

import (
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/monitor/monreporter"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/monitor/pcg007monitor"
)

// defaultMonitor 默认使用的 Monitor
var defaultMonitor monreporter.MonitorReporter

func init() {
	defaultMonitor = &EmptyReporter{}
}

// Initialize 初始化monitor资源
func Initialize() {
	log.Info("Init monitor")
	pcg007 := pcg007monitor.NewPCG007Monitor()
	defaultMonitor = pcg007
	go startDealWarning()
}

// GetMonitorReporter 获取当前监控告警上报者
func GetMonitorReporter() monreporter.MonitorReporter {
	return defaultMonitor
}

// SetMonitorRepoter 设置当前监控告警上报者
func SetMonitorRepoter(monitor monreporter.MonitorReporter) {
	defaultMonitor = monitor
}

// EmptyReporter 空的Reporter实现,用于测试时的初始化
type EmptyReporter struct {
}

// MonitorReport 上报监控告警
func (e EmptyReporter) MonitorReport(interface{}) error {
	return nil
}
