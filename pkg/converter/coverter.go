// Package worker 模型转换worker
package converter

import (
	"errors"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

const InternalError = "internal_error"

// IConverter 转模接口类
type IConverter interface {
	// Convert 转模
	Convert() define.TaskResult
}

// NewWorker 构造Worker
func NewWorker(task define.ConvertTask) (IConverter, error) {
	switch task.ConvertType {
	case define.Numerous2Trt:
		return &NumerousConverter{
			Task: task,
		}, nil
	default:
		return nil, errors.New("convertType配置错误")
	}
}
