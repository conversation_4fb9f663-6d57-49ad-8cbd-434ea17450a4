// Package impl 不同类型模型转换worker的实现
package converter

import (
	"bufio"
	"fmt"
	"io"
	"os"
	"os/exec"
	"strings"
	"sync"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/cls"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/commvar"
	workerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/worker"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

const (
	convertScriptPath = "/data/NumerousTensorRT"
	convertScriptName = "worker_main.sh"
	bufferSize        = 50 * 1024 * 1024 // stdout/stderr缓冲区大小
)

// NumerousConverter 无量转模器
type NumerousConverter struct {
	Task define.ConvertTask
}

func (n *NumerousConverter) createClsLog(line string, level string) *cls.ClsLog {
	return &cls.ClsLog{
		TaskID:             n.Task.TaskID,
		LogLevel:           level,
		Content:            line,
		OriginModelName:    n.Task.OriginModelName,
		OriginModelVersion: n.Task.OriginModelVersion,
		OutputModelName:    n.Task.OutputModelName,
		ContainerName:      commvar.GetCommVar().ContainerName,
	}
}

// processPipe 处理管道输出并写入文件
func (n *NumerousConverter) processPipe(pipe io.Reader, file *os.File, logLevel string) define.TaskResultMsg {
	conf := workerconfig.GetConfig()
	resultMsg := define.TaskResultMsg{}
	scanner := bufio.NewScanner(pipe)
	// 设置更大的缓冲区以处理很长的行
	// TODO(orlandochen): 如果超过bufferSize,程序会卡住,调用strace会显示write,这种情况要如何监控?
	buf := make([]byte, bufferSize)
	scanner.Buffer(buf, bufferSize)

	for scanner.Scan() {
		line := scanner.Text()
		file.WriteString(line)
		file.WriteString("\n")
		file.Sync()
		cls.SendLog(n.createClsLog(line, logLevel))
		// 错误码已经确定,不再处理
		if resultMsg.ErrCode != "" {
			continue
		}
		for _, item := range conf.Errors {
			if strings.Contains(line, item.ErrMsg) {
				resultMsg = define.TaskResultMsg{ErrCode: item.Code, ErrReason: item.Reason}
				break
			}
		}
	}
	if err := scanner.Err(); err != nil {
		// 记录扫描错误
		cls.SendLog(n.createClsLog(fmt.Sprintf("Scanner error: %v", err), "error"))
	}
	return resultMsg
}

func (n *NumerousConverter) createExecCommand() *exec.Cmd {
	opsetVersion := n.Task.NumerousParam.OpsetVersion
	if opsetVersion == 0 {
		opsetVersion = 13
	}
	conf := workerconfig.GetConfig()
	cmd := exec.Command("bash", "-c", fmt.Sprintf("cd %s; ./%s", convertScriptPath, convertScriptName))
	cmd.Env = append(os.Environ(),
		"ENV_TASK_ID="+n.Task.TaskID,
		"ENV_APP_GROUP_ID="+n.Task.AppGroupID,
		"ENV_OPERATOR="+n.Task.Operator,
		"ENV_SECRET_ID="+conf.ThirdParty.OpenAPI.SecretID,
		"ENV_SECRET_KEY="+conf.ThirdParty.OpenAPI.SecretKey,
		"ENV_MODEL_NAME="+n.Task.OriginModelName,
		"ENV_MODEL_VERSION="+n.Task.OriginModelVersion,
		"ENV_OUTPUT_MODEL_NAME="+n.Task.OutputModelName,
		"ENV_PREDICT_TARGET="+n.Task.NumerousParam.PredictTarget,
		"ENV_BATCH_SIZE="+string(n.Task.NumerousParam.BatchSize),
		"ENV_CPU_TARGET="+string(n.Task.NumerousParam.CpuTarget),
		"ENV_ENGINE_TYPE="+string(n.Task.NumerousParam.EngineType),
		"ENV_USE_FP16="+fmt.Sprintf("%v", n.Task.NumerousParam.UseFp16),
		"ENV_BUILD_REFITTABLE_ENGINE="+fmt.Sprintf("%v", n.Task.NumerousParam.BuildRefittableEngine),
		"ENV_OPSET_VERSION="+fmt.Sprintf("%d", opsetVersion),
		"ENV_USE_EVART_TOOL="+fmt.Sprintf("%v", n.Task.NumerousParam.UseEvartTool),
		"ENV_USER_INPUTS="+n.Task.NumerousParam.UserInputs,
		"ENV_ITEM_INPUTS="+n.Task.NumerousParam.ItemInputs,
		"ENV_CONVERT_WITH_REFIT="+fmt.Sprintf("%v", n.Task.NumerousParam.ConvertWithRefit),
		"ENV_FORCE_CONVERT_WITH_REFIT="+fmt.Sprintf("%v", n.Task.NumerousParam.ForceConvertWithRefit),
	)
	return cmd
}

// StartConvert 提交转模任务
func (n *NumerousConverter) Convert() define.TaskResult {
	execCommand := n.createExecCommand()
	stdoutPipe, err := execCommand.StdoutPipe()
	if err != nil {
		return define.TaskResult{
			Status: define.Failed,
			TaskResultMsg: define.TaskResultMsg{
				ErrCode:   InternalError,
				ErrReason: fmt.Sprintf("create stdout pipe failed: %s", err.Error()),
			},
		}
	}
	stderrPipe, err := execCommand.StderrPipe()
	if err != nil {
		return define.TaskResult{
			Status: define.Failed,
			TaskResultMsg: define.TaskResultMsg{
				ErrCode:   InternalError,
				ErrReason: fmt.Sprintf("create stderr pipe failed: %s", err.Error()),
			},
		}
	}
	logPath := fmt.Sprintf("%s/tasks/%s", convertScriptPath, n.Task.TaskID)
	// 确保日志目录存在
	if err := os.MkdirAll(logPath, 0755); err != nil {
		return define.TaskResult{
			Status: define.Failed,
			TaskResultMsg: define.TaskResultMsg{
				ErrCode:   InternalError,
				ErrReason: fmt.Sprintf("create log directory failed: %s", err.Error()),
			},
		}
	}
	// 打开日志文件（覆盖写入）
	stdoutFile, err := os.Create(fmt.Sprintf("%s/%s", logPath, "stdout.log"))
	if err != nil {
		return define.TaskResult{
			Status: define.Failed,
			TaskResultMsg: define.TaskResultMsg{
				ErrCode:   InternalError,
				ErrReason: fmt.Sprintf("open stdout log file failed: %s", err.Error()),
			},
		}
	}
	defer stdoutFile.Close()
	stderrFile, err := os.Create(fmt.Sprintf("%s/%s", logPath, "stderr.log"))
	if err != nil {
		return define.TaskResult{
			Status: define.Failed,
			TaskResultMsg: define.TaskResultMsg{
				ErrCode:   InternalError,
				ErrReason: fmt.Sprintf("open stderr log file failed: %s", err.Error()),
			},
		}
	}
	defer stderrFile.Close()
	if err := execCommand.Start(); err != nil {
		return define.TaskResult{
			Status: define.Failed,
			TaskResultMsg: define.TaskResultMsg{
				ErrCode:   InternalError,
				ErrReason: fmt.Sprintf("start convert command failed: %s", err.Error()),
			},
		}
	}

	var stdoutResultMsg, stderrResultMsg define.TaskResultMsg
	var pipeWaitGroup sync.WaitGroup
	pipeWaitGroup.Add(2)
	go func() {
		stdoutResultMsg = n.processPipe(stdoutPipe, stdoutFile, "stdout")
		pipeWaitGroup.Done()
	}()
	go func() {
		stderrResultMsg = n.processPipe(stderrPipe, stderrFile, "stderr")
		pipeWaitGroup.Done()
	}()
	// 等待协程结束
	pipeWaitGroup.Wait()
	retResultMsg := stderrResultMsg
	if retResultMsg.ErrCode == "" {
		retResultMsg = stdoutResultMsg
	}
	if err := execCommand.Wait(); err != nil {
		return define.TaskResult{Status: define.Failed, TaskResultMsg: retResultMsg}
	}
	return define.TaskResult{Status: define.Success}
}
