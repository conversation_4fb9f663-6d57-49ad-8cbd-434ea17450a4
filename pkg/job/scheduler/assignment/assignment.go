// Package assignment 实现任务分配
package assignment

import (
	"sort"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/samber/lo"

	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	wkinst "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/instance"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

// Initialize 初始化
func Initialize() {
	// 启动处理任务分配定时器
	go startAssignmentTimer()
}

// startAssignmentTimer 启动处理任务分配定时器
func startAssignmentTimer() {
	const interval = 5 * time.Second
	timer := time.NewTimer(interval)
	defer timer.Stop()
	for range timer.C {
		doAssignment()
		timer.Reset(interval)
	}
}

func checkSetAssignable(setConf *schedulerconfig.WorkerSet, pendingTask *define.ConvertTask) bool {
	if !lo.Contains(setConf.SupportConvertTypes, pendingTask.ConvertType) {
		log.Debugf("Not support convert type:%s", pendingTask.ConvertType)
		return false
	}
	if setConf.GPUType != pendingTask.GpuType {
		log.Debugf("Not support gpu type:%s", pendingTask.GpuType)
		return false
	}
	if _, ok := setConf.SupportEngineInfo[pendingTask.NumerousParam.EngineType]; !ok {
		log.Debugf("Not support engine type:%s", pendingTask.NumerousParam.EngineType)
		return false
	} else {
		// TODO(orlandochen): 检查版本
	}

	if setConf.IsPrivate {
		if lo.Contains(setConf.AppGroupIDs, pendingTask.AppGroupID) {
			return true
		}
		if lo.Contains(setConf.ServerIDs, pendingTask.ServerID) {
			return true
		}
		if lo.Contains(setConf.ModelNames, pendingTask.OriginModelName) {
			return true
		}
		return false
	}
	return true
}

func tryAssignWorker(pendingTask *define.ConvertTask, workers []wkinst.WorkerInstance) bool {
	for _, worker := range workers {
		if assignedTasks := schedulertask.GetAssignmentTasksByWorker(worker.ContainerName); len(assignedTasks) > 0 {
			continue
		}
		containerName := worker.ContainerName
		pendingTask.Status = define.Running
		pendingTask.StartTime = time.Now()
		pendingTask.WorkerAssignment = containerName
		if err := schedulertask.ReplaceConvertTask(*pendingTask); err != nil {
			log.Errorf("ReplaceConvertTask failed:%s", err.Error())
		}
		return true
	}
	return false
}

var getWaitingTasks = func(task *define.ConvertTask) bool {
	return task.Status == define.Waiting
}

func assignTaskToWorker(pendingTask *define.ConvertTask,
	sets map[string][]wkinst.WorkerInstance,
	setType string,
	workerSetconf schedulerconfig.WorkerSetConfig) (assigned bool, assignedSetName string) {
	for setName, workers := range sets {
		setConf, _ := workerSetconf.Sets[setName]
		if !checkSetAssignable(&setConf, pendingTask) {
			log.Debugf("%s set:%s not fit for task:%s", setType, setName, pendingTask.TaskID)
			continue
		}
		if tryAssignWorker(pendingTask, workers) {
			log.Debugf("assign task:%s to %s set:%s", pendingTask.TaskID, setType, setName)
			return true, setName
		}
	}
	return false, ""
}

// doAssignment 处理任务分配
func doAssignment() {
	allPendingTasks := schedulertask.GetConvertTasksByFun(getWaitingTasks)
	if len(allPendingTasks) == 0 {
		return
	}

	healthyFunc := func(inst *wkinst.WorkerInstance) bool {
		return inst.Healthy
	}
	allWorkers := wkinst.GetWorkerInstsWithCB(healthyFunc, false, 0)
	// 按worker的心跳时间排序,优先分配给最近上报心跳时间的worker
	sort.Slice(allWorkers, func(i, j int) bool {
		return allWorkers[i].LastHeartbeat.After(allWorkers[j].LastHeartbeat)
	})
	// 按优先级排序(数值大的在前),然后按截止时间排序(时间早的在前)
	sort.Slice(allPendingTasks, func(i, j int) bool {
		// 先按Priority排序,数值大的排在前面
		if allPendingTasks[i].Priority != allPendingTasks[j].Priority {
			return allPendingTasks[i].Priority > allPendingTasks[j].Priority
		}
		// Priority相同时,按Deadline排序,时间早的排在前面
		return allPendingTasks[i].Deadline.Before(allPendingTasks[j].Deadline)
	})
	workerSetconf := schedulerconfig.GetConfig().WorkerSetConf
	log.Debugf("workerSetconf:%+v", workerSetconf)

	// 按SET组织worker实例, SetName -> []WorkerInstance
	workersForPublicSet := make(map[string][]wkinst.WorkerInstance)
	workersForPrivateSet := make(map[string][]wkinst.WorkerInstance)
	for _, worker := range allWorkers {
		setName := worker.SetName
		setInfo, ok := workerSetconf.Sets[setName]
		if !ok {
			// TODO(orlandochen): 告警
			log.Errorf("Set %s not found in config", setName)
			continue
		}
		if setInfo.IsPrivate {
			workersForPrivateSet[setName] = append(workersForPrivateSet[setName], worker)
		} else {
			workersForPublicSet[setName] = append(workersForPublicSet[setName], worker)
		}
	}
	log.Debugf("workersForPublicSet:%+v", workersForPublicSet)
	log.Debugf("workersForPrivateSet:%+v", workersForPrivateSet)
	// 遍历所有Pending任务,分配Worker
	for _, pendingTask := range allPendingTasks {
		// 一个output model name只能有一个running任务,防止多个任务之间发生冲突
		outputModelName := pendingTask.OutputModelName
		if running := schedulertask.GetOutputModelNameRunningTasks(outputModelName); len(running) > 0 {
			log.Debugf("output model name:%s already exists running tasks:%+v", outputModelName, running)
			continue
		}
		isAssigned, _ := assignTaskToWorker(&pendingTask, workersForPrivateSet, "private", workerSetconf)
		if !isAssigned {
			isAssigned, _ = assignTaskToWorker(&pendingTask, workersForPublicSet, "public", workerSetconf)
		}
		log.Debugf("doAssignment pendingTask:%+v, isAssigned:%t", pendingTask, isAssigned)
	}
}
