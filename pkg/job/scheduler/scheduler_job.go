// Package scheduler 实现一些定时任务/单次任务执行
package scheduler

import (
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/scheduler/assignment"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/scheduler/heartbeat"
)

// SchedulerJobInitialize 初始化
func SchedulerJobInitialize() {
	log.Info("Init scheduler job")
	assignment.Initialize()
	heartbeat.Initialize()
}
