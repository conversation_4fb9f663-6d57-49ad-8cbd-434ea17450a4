package cleanup

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
	taskhistory "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/taskhistory"
)

func Initialize() {
	go startCleanConvertTaskTimer()
}

func doCleanConvertTask() {
	log.Infof("Start to clean finished task")

	convertTaskExpireTime := time.Duration(schedulerconfig.GetConfig().Job.Cleanup.ConvertTaskExpireTime) * time.Second
	var getFinishedTasks = func(task *define.ConvertTask) bool {
		if !task.Status.IsFinished() {
			return false
		}
		if task.EndTime.Add(convertTaskExpireTime).After(time.Now()) {
			return false
		}
		return true
	}

	cleanTasks := schedulertask.GetConvertTasksByFun(getFinishedTasks)
	for _, clean := range cleanTasks {
		log.Infof("Go to clean finished task:%s", clean.TaskID)
		taskHisItem := &define.ConvertTaskHistory{ConvertTask: clean}
		if err := taskhistory.ReplaceConvertTaskHistory(taskHisItem); err != nil {
			log.Errorf("Replace convert task history failed:%s", err.Error())
		}
		schedulertask.DeleteConvertTask(clean.TaskID)
	}
}

func startCleanConvertTaskTimer() {
	log.Infof("Start clean convert task timer")
	interval := time.Duration(schedulerconfig.GetConfig().Job.Cleanup.CleanConvertTaskInterval) * time.Second
	timer := time.NewTimer(interval)
	defer timer.Stop()
	for range timer.C {
		doCleanConvertTask()
		interval = time.Duration(schedulerconfig.GetConfig().Job.Cleanup.CleanConvertTaskInterval) * time.Second
		timer.Reset(interval)
	}
}
