package heartbeat

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	wkinst "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/instance"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

// Initialize 初始化
func Initialize() {
	initReportHeartbeat()
	initCheckHeartbeat()
	initCheckPolaris()
}

func initCheckHeartbeat() {
	conf := schedulerconfig.GetConfig()
	firstCheckDuration := time.Duration(conf.Heartbeat.Check.StartupCheckInterval) * time.Second
	checkDuration := time.Duration(conf.Heartbeat.Check.CheckInterval) * time.Second
	go startCheckHeartbeat(firstCheckDuration, checkDuration)
}

// startCheckHeartbeat 启动心跳检查定时器
func startCheckHeartbeat(firstCheckDuration time.Duration, checkDuration time.Duration) {
	timer := time.NewTimer(firstCheckDuration)
	defer timer.Stop()
	for range timer.C {
		doCheckHeartbeat()
		timer.Reset(checkDuration)
	}
}

func resetTaskStatusForTimeoutWorker(instance wkinst.WorkerInstance) {
	assignments := schedulertask.GetAssignmentTasksByWorker(instance.ContainerName)
	for _, assign := range assignments {
		if assign.Deadline.Before(time.Now()) {
			log.Errorf("worker:%+v timeout, reset task:%s status to failed", instance.WorkerBaseInfo, assign.TaskID)
			assign.Status = define.Failed
		} else {
			log.Infof("worker:%+v timeout, reset task:%s status to waiting", instance.WorkerBaseInfo, assign.TaskID)
			assign.Status = define.Waiting
		}
		assign.WorkerAssignment = ""
		schedulertask.ReplaceConvertTask(assign)
	}
}

func doCheckHeartbeat() {
	log.Info("Start to check heartbeat")
	conf := schedulerconfig.GetConfig()
	workerTimeoutDuration := time.Duration(conf.Heartbeat.Check.WorkerTimeout) * time.Second
	dealTimeoutWorkersOnce := conf.Heartbeat.Check.DealTimeoutWorkersOnce

	timeoutFunc := func(instance *wkinst.WorkerInstance) bool {
		return time.Since(instance.LastHeartbeat) > workerTimeoutDuration
	}
	timeoutWorkers := wkinst.GetWorkerInstsWithCB(timeoutFunc, true, dealTimeoutWorkersOnce)
	for _, worker := range timeoutWorkers {
		log.Infof("Worker:%+v timeout, go to delete it", worker.WorkerBaseInfo)
		// report里面的缓存也要清理
		ReportServerInstHeartbeat(ReportHeartbeat{
			WorkerBaseInfo: worker.WorkerBaseInfo,
			ReportType:     ReportTypeHeartbeat,
			WorkerStatus:   define.Inactive,
		})
		if err := wkinst.DeleteWorkerInst(worker); err != nil {
			log.Errorf("DeleteWorkerInst failed:%s", err.Error())
		}
		resetTaskStatusForTimeoutWorker(worker)
	}
}
