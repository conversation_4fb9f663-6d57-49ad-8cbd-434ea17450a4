package heartbeat

import (
	"hash/fnv"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	wkinst "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/instance"
)

var (
	// reportInterval 上报心跳间隔
	reportInterval = 1 * time.Minute
	// reportQueueSize 上报心跳channel大小
	reportQueueSize = 1000
	// reportQueueNum 上报心跳并发队列数量
	reportQueueNum = 5
	// reportHeartbeatQueue 上报心跳队列数组
	reportHeartbeatQueue []chan ReportHeartbeat
	// reportHeartbeatMap 心跳上报存储Map,用以控制上报频率,不用加锁,每个map只有一个go协程读写
	reportHeartbeatMap []map[string]time.Time
)

type ReportType string

const (
	ReportTypeHeartbeat ReportType = "heartbeat"
	ReportTypeHealthy   ReportType = "healthy"
)

type ReportHeartbeat struct {
	define.WorkerBaseInfo
	ReportType    ReportType
	LastHeartbeat time.Time
	WorkerStatus  define.WorkerStatus
	WorkerHealthy bool
}

func initReportHeartbeat() {
	conf := schedulerconfig.GetConfig()
	reportInterval = time.Duration(conf.Heartbeat.Report.Interval) * time.Second
	reportQueueSize = conf.Heartbeat.Report.QueueSize
	reportQueueNum = conf.Heartbeat.Report.QueueNum
	log.Infof("interval:%d, queueSize:%d, queueNum:%d",
		int(reportInterval.Seconds()), reportQueueSize, reportQueueNum)
	for i := 0; i < reportQueueNum; i++ {
		reportHeartbeatQueue = append(reportHeartbeatQueue, make(chan ReportHeartbeat, reportQueueSize))
		reportHeartbeatMap = append(reportHeartbeatMap, make(map[string]time.Time))
		go startReportHeartbeat(reportHeartbeatQueue[i], reportHeartbeatMap[i])
	}
}

func getReportQueueIdx(str string) uint32 {
	h := fnv.New32a()
	h.Write([]byte(str))
	return h.Sum32() % uint32(reportQueueNum)
}

func dealReportHeartbeat(report ReportHeartbeat, reportMap map[string]time.Time) {
	if report.WorkerStatus == define.Inactive {
		log.Infof("Delete %s", report.ContainerName)
		delete(reportMap, report.ContainerName)
		return
	}

	last, hasLast := reportMap[report.ContainerName]
	if hasLast && time.Since(last) < reportInterval {
		// 节点心跳上报小于上报间隔则直接跳过
		return
	}
	worker, hasWorker := wkinst.GetWorkerInst(report.ContainerName)
	if !hasWorker {
		worker = wkinst.WorkerInstance{
			WorkerBaseInfo: report.WorkerBaseInfo,
			Status:         define.Active,
			Healthy:        true,
			LastHeartbeat:  report.LastHeartbeat,
		}
	}
	worker.Status = define.Active
	worker.LastHeartbeat = report.LastHeartbeat
	wkinst.ReplaceWorkerInst(worker)
	reportMap[report.ContainerName] = report.LastHeartbeat
}

func dealReportHealthy(report ReportHeartbeat) {
	inst, ok := wkinst.GetWorkerInst(report.ContainerName)
	if !ok {
		return
	}
	inst.Healthy = report.WorkerHealthy
	if err := wkinst.ReplaceWorkerInst(inst); err != nil {
		log.Errorf("ReplaceWorkerInst err:%v", err)
	}
}

func startReportHeartbeat(reportCH <-chan ReportHeartbeat, reportMap map[string]time.Time) {
	for worker := range reportCH {
		log.Debugf("Heartbeat worker: %+v", worker)
		// 状态为inactive的是已经被清理了,这里的缓存也要清理掉
		if worker.ReportType == ReportTypeHeartbeat {
			dealReportHeartbeat(worker, reportMap)
		} else if worker.ReportType == ReportTypeHealthy {
			dealReportHealthy(worker)
		}
	}
}

// addToReportHeartbeatCH 加入到上报心跳队列中
func addToReportHeartbeatCH(worker ReportHeartbeat) {
	// 打印所有心跳上报和丢失心跳的记录
	if worker.ContainerName == "" {
		return
	}
	idx := getReportQueueIdx(worker.ContainerName)
	select {
	case reportHeartbeatQueue[idx] <- worker:
	default:
	}
}

// ReportServerInstHeartbeat 上报心跳
func ReportServerInstHeartbeat(worker ReportHeartbeat) {
	addToReportHeartbeatCH(worker)
}
