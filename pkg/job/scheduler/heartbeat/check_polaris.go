package heartbeat

import (
	"sync/atomic"
	"time"

	"git.code.oa.com/polaris/polaris-go/api"
	"git.code.oa.com/trpc-go/trpc-go/log"
	trpcplugin "git.code.oa.com/trpc-go/trpc-go/plugin"
	trpcnaming "git.code.oa.com/trpc-go/trpc-naming-polaris"
	"github.com/samber/lo"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/commvar"
	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	wkinst "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/instance"
)

const (
	keyContainerName = "container_name"
	keySetName       = "internal-set-name"
)

var flowId uint64
var consumer api.ConsumerAPI

func initCheckPolaris() {
	plugin := trpcplugin.Get("selector", "polaris")
	if plugin == nil {
		log.Errorf("get selector polaris failed")
		return
	}
	selectorFactory, ok := plugin.(*trpcnaming.SelectorFactory)
	if !ok {
		log.Fatal("not found")
	}
	polarisSdk := selectorFactory.GetSDKCtx()
	consumer = api.NewConsumerAPIByContext(polarisSdk)
	go startCheckWorkerPolaris()
}

func startCheckWorkerPolaris() {
	const interval = 30 * time.Second
	timer := time.NewTimer(interval)
	defer timer.Stop()
	for range timer.C {
		doCheckWorkerPolaris()
		timer.Reset(interval)
	}
}

func doCheckWorkerPolaris() {
	getInstancesReq := &api.GetAllInstancesRequest{}
	getInstancesReq.FlowID = atomic.AddUint64(&flowId, 1)
	getInstancesReq.Namespace = commvar.GetCommVar().Namespace
	getInstancesReq.Service = schedulerconfig.GetConfig().WorkerSetConf.WorkerService
	instances, err := consumer.GetAllInstances(getInstancesReq)
	if err != nil {
		log.Errorf("GetInstances failed, err:%s", err.Error())
		return
	}

	unhealthyFromPolaris := make(map[define.WorkerBaseInfo]struct{})
	for _, inst := range instances.GetInstances() {
		containerName := inst.GetMetadata()[keyContainerName]
		setName := inst.GetMetadata()[keySetName]
		if containerName == "" {
			continue
		}
		if inst.IsIsolated() || inst.GetWeight() == 0 || !inst.IsHealthy() {
			workerInfo := define.WorkerBaseInfo{
				ContainerName: containerName,
				SetName:       setName,
			}
			unhealthyFromPolaris[workerInfo] = struct{}{}
		}
	}

	unhealthyFunc := func(inst *wkinst.WorkerInstance) bool {
		return !inst.Healthy
	}
	unhealthy := wkinst.GetWorkerInstsWithCB(unhealthyFunc, false, 0)
	unhealthyFromWorkers :=
		lo.SliceToMap(unhealthy, func(item wkinst.WorkerInstance) (define.WorkerBaseInfo, struct{}) {
			return item.WorkerBaseInfo, struct{}{}
		})

	// 遍历从北极星获取到的unhealthy容器,发现有不存在于worker实例中的,则更改worker的healthy状态为false
	for worker := range unhealthyFromPolaris {
		if _, ok := unhealthyFromWorkers[worker]; !ok {
			log.Infof("worker:%+v is unhealthy", worker)
			ReportServerInstHeartbeat(ReportHeartbeat{
				WorkerBaseInfo: worker,
				ReportType:     ReportTypeHealthy,
				WorkerHealthy:  false,
			})
		}
	}
	// 遍历从worker实例获取到的unhealthy容器,发现有不存在于北极星中的,则更改worker的healthy状态为true
	for worker := range unhealthyFromWorkers {
		if _, ok := unhealthyFromPolaris[worker]; !ok {
			log.Infof("worker:%+v is healthy", worker)
			ReportServerInstHeartbeat(ReportHeartbeat{
				WorkerBaseInfo: worker,
				ReportType:     ReportTypeHealthy,
				WorkerHealthy:  true,
			})
		}
	}
	// TODO(orlandochen): 节点无流量的时间太久了, 要告警一下
}
