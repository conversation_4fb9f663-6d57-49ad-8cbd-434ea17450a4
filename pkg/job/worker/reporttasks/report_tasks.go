package reporttasks

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"

	workertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/worker/task"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/thirdparty/scheduler"
)

const (
	reportTasksInterval = 15 * time.Second
)

var reportTasksChannel = make(chan struct{}, 1)

func Initialize() {
	go startReportTasks()
}

func startReportTasks() {
	timer := time.NewTimer(reportTasksInterval)
	defer timer.Stop()
	for {
		select {
		case <-timer.C:
			log.Debug("Send to report tasks channel")
			select {
			case reportTasksChannel <- struct{}{}:
			default:
			}
		case <-reportTasksChannel:
			doReportTasks()
			// 定时器的channel里面有数据,stop会失败,要清空
			if !timer.Stop() {
				select {
				case <-timer.C:
				default:
				}
			}
			timer.Reset(reportTasksInterval)
		}
	}
}

func doReportTasks() {
	log.Infof("Start to report tasks")
	allTasks := workertask.GetAllReportTasks()
	scheduler.ReportConvertTasksResult(allTasks)
}

// ReportTasksImmediately 马上触发一次任务上报
func ReportTasksImmediately() {
	select {
	case reportTasksChannel <- struct{}{}:
	default:
	}
}
