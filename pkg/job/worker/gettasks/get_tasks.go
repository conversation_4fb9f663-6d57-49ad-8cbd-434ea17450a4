// Package task 接受处理任务
package gettasks

import (
	"time"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/samber/lo"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/converter"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/worker/reporttasks"
	workertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/worker/task"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/thirdparty/scheduler"
)

// Initialize 初始化
func Initialize() {
	// 启动接受任务定时器
	go startGetTasksTimer()
}

// startGetTasksTimer 启动获取任务定时器
func startGetTasksTimer() {
	const interval = 5 * time.Second
	timer := time.NewTimer(interval)
	defer timer.Stop()
	for range timer.C {
		doGetTasks()
		timer.Reset(interval)
	}
}

func cleanWorkerTasks(schedulerTasks []define.ConvertTask, workerTasks []define.ReportTask) {
	for _, wtask := range workerTasks {
		stask, ok := lo.Find(schedulerTasks,
			func(c define.ConvertTask) bool { return c.TaskID == wtask.TaskID })
		if !ok {
			// worker中存在,scheduler中不存在
			if wtask.TaskResult.Status == define.Running {
				// TODO(orlandochen): 这个task在scheduler中找不到,但是在worker中又处于running状态,要停止并告警
				continue
			} else {
				// 否则进行清理
				workertask.DeleteReportTask(wtask.TaskID)
			}
		} else {
			// worker中存在,scheduler中也存在
			if !wtask.TaskResult.Status.IsFinished() && stask.Status.IsFinished() {
				// TODO(orlandochen): worker的状态为非finished,scheduler的状态为finished,有异常,需要告警
				continue
			}
			log.Debugf("IConverter task:%s is running, waiting for idle", wtask.TaskResult)
		}
	}
}

// doGetTasks 处理任务分配
func doGetTasks() {
	schedulerTasks, err := scheduler.GetConvertTasks()
	if err != nil {
		log.Error(err)
		return
	}
	log.Debugf("receive scheduler tasks:%+v", schedulerTasks)
	// 先清理worker中的任务
	workerTasks := workertask.GetAllReportTasks()
	cleanWorkerTasks(schedulerTasks, workerTasks)

	schedulerRunningTasks := make([]define.ConvertTask, 0)
	for _, task := range schedulerTasks {
		if task.Status != define.Running {
			log.Debugf("get task:%s status:%s not running, skip", task.TaskID, task.Status)
			continue
		}
		schedulerRunningTasks = append(schedulerRunningTasks, task)
		if len(schedulerRunningTasks) > 1 {
			log.Error("Get %d running task", len(schedulerRunningTasks))
		}
	}
	if len(schedulerRunningTasks) == 0 {
		return
	}

	running := schedulerRunningTasks[0]
	workerTasks = workertask.GetAllReportTasks()
	for _, task := range workerTasks {
		// 只要worker有running的任务,则都不执行其他任务
		if task.TaskResult.Status == define.Running {
			log.Debugf("worker task:%s is running, waiting for it finished", task.TaskID)
			return
		}
		// 任务ID相同但是状态不同,需要进行状态上报
		if task.TaskID == running.TaskID {
			log.Debugf("worker task:%s status:%s, report to scheduler", task.TaskID, task.TaskResult.Status)
			reporttasks.ReportTasksImmediately()
			return
		}
	}

	reportTask := define.ReportTask{
		TaskID: running.TaskID,
		TaskResult: define.TaskResult{
			Status: define.Running,
		},
	}
	workertask.ReplaceReportTask(reportTask)
	log.Infof("Get task:%s from scheduler, start to process", running.TaskID)
	go processConvertTask(running)
}

func processConvertTask(task define.ConvertTask) {
	// 开始执行转模任务
	converterImpl, err := converter.NewWorker(task)
	if err != nil {
		log.Errorf("New worker error:%s", err.Error())
		return
	}
	log.Infof("Start convert task:%s", task.TaskID)
	result := converterImpl.Convert()
	log.Infof("Finish convert task:%s result:%v", task.TaskID, result)
	reportTask := define.ReportTask{
		TaskID: task.TaskID,
		TaskResult: define.TaskResult{
			Status:        lo.Ternary(result.Status.IsFinished(), result.Status, define.Failed),
			EndTime:       time.Now(),
			TaskResultMsg: result.TaskResultMsg,
		},
	}
	workertask.ReplaceReportTask(reportTask)
	reporttasks.ReportTasksImmediately()
}
