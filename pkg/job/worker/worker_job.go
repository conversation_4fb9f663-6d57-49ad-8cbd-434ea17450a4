// Package job 实现一些定时任务/单次任务执行
package worker

import (
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/cls"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/worker/gettasks"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/worker/reporttasks"
)

// WorkerJobInitialize 初始化
func WorkerJobInitialize() {
	log.Info("Init worker job")
	cls.Initialize()
	gettasks.Initialize()
	reporttasks.Initialize()
}
