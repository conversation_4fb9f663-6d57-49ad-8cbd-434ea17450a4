// Package commvar 全局公共变量,启动时候初始化
package commvar

import (
	"sync/atomic"

	"git.code.oa.com/trpc-go/trpc-go"
	"git.code.oa.com/trpc-go/trpc-go/log"
)

func init() {
	setCommVar(&Variable{})
}

// Initialize  初始化
func Initialize() {
	initVariable()
}

var gloVar atomic.Value

// Variable 全局变量结构体
type Variable struct {
	FullSetName   string
	ContainerName string
	EnvName       string
	Namespace     string
	App           string
	Server        string
}

// initVariable 初始化全局变量
func initVariable() {
	vari := &Variable{
		EnvName:       trpc.GlobalConfig().Global.EnvName,
		FullSetName:   trpc.GlobalConfig().Global.FullSetName,
		ContainerName: trpc.GlobalConfig().Global.ContainerName,
		Namespace:     trpc.GlobalConfig().Global.Namespace,
		App:           trpc.GlobalConfig().Server.App,
		Server:        trpc.GlobalConfig().Server.Server,
	}
	log.Infof("variable:%+v", vari)
	setCommVar(vari)
}

// GetCommVar 获取当前全局变量
func GetCommVar() *Variable {
	return gloVar.Load().(*Variable)
}

func setCommVar(variable *Variable) {
	gloVar.Store(variable)
}
