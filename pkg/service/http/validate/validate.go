package validate

import (
	"errors"
	"strings"

	"github.com/go-playground/locales/en"
	ut "github.com/go-playground/universal-translator"
	"github.com/go-playground/validator/v10"
	entrans "github.com/go-playground/validator/v10/translations/en"
)

var validate *validator.Validate
var trans ut.Translator

func init() {
	en2 := en.New()
	ut2 := ut.New(en2, en2)
	trans, _ = ut2.GetTranslator("en")
	validate = validator.New()
	_ = entrans.RegisterDefaultTranslations(validate, trans)
}

// Validate 校验接口
func Validate(s interface{}) error {
	err := validate.Struct(s)
	if err == nil {
		return nil
	}

	var errMsg []string
	for _, e := range err.(validator.ValidationErrors) {
		errMsg = append(errMsg, e.Translate(trans))
	}
	return errors.New(strings.Join(errMsg, ","))
}
