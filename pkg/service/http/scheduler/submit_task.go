package scheduler

import (
	"encoding/json"
	"fmt"
	"time"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/samber/lo"

	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

func init() {
	path := define.UrlSubmitTask
	if err := schttp.RegisterHandler(path, &submitTaskHandler{}, schttp.Json); err != nil {
		log.Fatalf("Register api handler:%s error:%s", path, err.Error())
	}
}

// submitTaskHandler 提交转模任务处理
type submitTaskHandler struct {
}

// Handle http请求入口处理函数
func (r *submitTaskHandler) Handle(request schttp.HttpRequest) schttp.HttpResponse {
	response := &define.SubmitTaskRsp{}
	response.RetCode = schttp.OK
	switch request.Method {
	case schttp.MethodPost:
		handleSubmitConvert(request, response)
	default:
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("http method error:%s", request.Method)
	}
	return schttp.HttpResponse{Response: response}
}

func handleSubmitConvert(request schttp.HttpRequest, response *define.SubmitTaskRsp) {
	if request.Body == "" {
		response.RetCode = schttp.BadRequest
		response.Message = "request body is empty"
		return
	}
	var submitTaskReq define.SubmitTaskReq
	if err := json.Unmarshal([]byte(request.Body), &submitTaskReq); err != nil {
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("unmarshal json error: %s", err.Error())
		return
	}
	convertTask, err := submitConvertTask(&submitTaskReq)
	if err != nil {
		response.RetCode = schttp.InternalServerError
		response.Message = err.Error()
		return
	}
	response.Data = convertTask
}

func checkSubmitTaskReq(req *define.SubmitTaskReq) error {
	if req.TaskID == "" {
		return fmt.Errorf("taskID is empty")
	}
	if req.OriginModelName == "" {
		return fmt.Errorf("OriginModelName is empty")
	}
	if req.OriginModelVersion == "" {
		return fmt.Errorf("OriginModelVersion is empty")
	}
	if req.OutputModelName == "" {
		return fmt.Errorf("OutputModelName is empty")
	}
	if req.OutputModelVersion == "" {
		return fmt.Errorf("OutputModelVersion is empty")
	}
	if req.GpuType == "" {
		return fmt.Errorf("GpuType is empty")
	}
	if req.ConvertType == "" {
		return fmt.Errorf("ConvertType is empty")
	}
	// 最后要检查task_id是不是已经存在
	if _, ok := task.GetConvertTaskByID(req.TaskID); ok {
		return fmt.Errorf("taskID %s already exists", req.TaskID)
	}
	return nil
}

func getTaskPriority(config *schedulerconfig.Config, task *define.SubmitTaskReq) int {
	for _, rule := range config.TaskPriority.Rules {
		if lo.Contains(rule.AppGroupIDs, task.AppGroupID) {
			return rule.Priority
		}
		if lo.Contains(rule.ServerIDs, task.ServerID) {
			return rule.Priority
		}
		if lo.Contains(rule.OriginModelNames, task.OriginModelName) {
			return rule.Priority
		}
	}
	return config.TaskPriority.DefaultPriority
}

func submitConvertTask(req *define.SubmitTaskReq) (define.ConvertTask, error) {
	if err := checkSubmitTaskReq(req); err != nil {
		return define.ConvertTask{}, err
	}
	log.Infof("Marshal convert param--------:%+v", req.ConvertParam)
	config := schedulerconfig.GetConfig()
	convertTask := define.ConvertTask{
		TaskID:       req.TaskID,
		ConvertParam: req.ConvertParam,
		Status:       define.Waiting,
		CreateTime:   time.Now(),
		Deadline:     time.Now().Add(time.Hour),
		Priority:     getTaskPriority(config, req),
	}
	if err := task.ReplaceConvertTask(convertTask); err != nil {
		return define.ConvertTask{}, err
	}
	return convertTask, nil
}
