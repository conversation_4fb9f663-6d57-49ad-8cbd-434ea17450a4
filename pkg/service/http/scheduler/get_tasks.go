package scheduler

import (
	"encoding/json"
	"fmt"
	"time"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	workerJob "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/job/scheduler/heartbeat"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

func init() {
	path := define.UrlGetTasks
	if err := schttp.RegisterHandler(path, &getTasksHandler{}, schttp.Json); err != nil {
		log.Fatalf("Register api handler:%s error:%s", path, err.Error())
	}
}

// getTasksHandler worker获取任务处理
type getTasksHandler struct {
}

// Handle http请求入口处理函数
func (r *getTasksHandler) Handle(request schttp.HttpRequest) schttp.HttpResponse {
	response := &define.GetTasksRsp{}
	response.RetCode = schttp.OK
	switch request.Method {
	case schttp.MethodPost:
		handleGetTasks(request, response)
	default:
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("http请求方法错误:%s", request.Method)
	}
	return schttp.HttpResponse{Response: response}
}

func handleGetTasks(request schttp.HttpRequest, response *define.GetTasksRsp) {
	getTasksReq := define.GetTasksReq{}
	if err := json.Unmarshal([]byte(request.Body), &getTasksReq); err != nil {
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("unmarshal json error: %s", err.Error())
		return
	}
	containerName := getTasksReq.ContainerName
	setName := getTasksReq.SetName
	if containerName == "" || setName == "" {
		response.RetCode = schttp.BadRequest
		response.Message = "ContainerName or SetName is empty"
		return
	}
	// 上报心跳
	workerJob.ReportServerInstHeartbeat(workerJob.ReportHeartbeat{
		WorkerBaseInfo: getTasksReq.WorkerBaseInfo,
		ReportType:     workerJob.ReportTypeHeartbeat,
		LastHeartbeat:  time.Now(),
		WorkerStatus:   define.Active,
	})
	// 查询分配给worker的任务
	response.Data = schedulertask.GetAssignmentTasksByWorker(containerName)
}
