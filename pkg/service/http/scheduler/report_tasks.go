package scheduler

import (
	"encoding/json"
	"fmt"
	"time"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	schedulermetric "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/metric/scheduler"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
)

func init() {
	path := define.UrlReportTasks
	if err := schttp.RegisterHandler(path, &reportTasksHandler{}, schttp.Json); err != nil {
		log.Fatalf("Register api handler:%s error:%s", path, err.Error())
	}
}

// getTasksHandler worker获取任务处理
type reportTasksHandler struct {
}

// Handle http请求入口处理函数
func (u *reportTasksHandler) Handle(request schttp.HttpRequest) schttp.HttpResponse {
	response := &define.ReportTasksRsp{}
	response.RetCode = schttp.OK
	switch request.Method {
	case schttp.MethodPost:
		handleReportTasks(request, response)
	default:
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("http请求方法错误:%s", request.Method)
	}
	return schttp.HttpResponse{Response: response}
}

func handleReportTasks(request schttp.HttpRequest, response *define.ReportTasksRsp) {
	if request.Body == "" {
		response.RetCode = schttp.BadRequest
		response.Message = "request body is empty"
		return
	}
	var reportTaskReq define.ReportTasksReq
	if err := json.Unmarshal([]byte(request.Body), &reportTaskReq); err != nil {
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("unmarshal json error: %s", err.Error())
		return
	}
	for _, report := range reportTaskReq.Tasks {
		if err := updateTaskStatus(report); err != nil {
			log.Errorf("update task:%s status failed:%s", report.TaskID, err.Error())
		}
	}
}

func updateTaskStatus(report define.ReportTask) error {
	convertTask, ok := schedulertask.GetConvertTaskByID(report.TaskID)
	if !ok {
		return fmt.Errorf("convert task:%s not found", report.TaskID)
	}
	if convertTask.Status == report.TaskResult.Status {
		return nil
	}
	convertTask.Status = report.TaskResult.Status
	if convertTask.Status.IsFinished() {
		convertTask.EndTime = report.TaskResult.EndTime
		if convertTask.EndTime.IsZero() {
			convertTask.EndTime = time.Now()
		}
		convertTask.WorkerAssignment = ""
		convertTask.TaskResultMsg = report.TaskResult.TaskResultMsg
		convertTask.TaskProcess = report.TaskProgress
		schedulermetric.ReportConvertTask(&convertTask)
	}
	if err := schedulertask.ReplaceConvertTask(convertTask); err != nil {
		return err
	}
	return nil
}
