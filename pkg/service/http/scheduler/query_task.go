package scheduler

import (
	"encoding/json"
	"fmt"

	schttp "git.code.oa.com/RondaServing/ServingController/http"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	schedulertask "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/resource/scheduler/task"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/service/http/validate"
)

func init() {
	path := define.UrlQueryTask
	if err := schttp.RegisterHandler(path, &queryTaskHandler{}, schttp.Json); err != nil {
		log.Fatalf("Register api handler:%s error:%s", path, err.Error())
	}
	path = define.UrlNewQueryTask
	if err := schttp.RegisterHandler(path, &queryTaskHandler{}, schttp.Json); err != nil {
		log.Fatalf("Register api handler:%s error:%s", path, err.Error())
	}
}

type queryTaskHandler struct {
}

// Handle http请求入口处理函数
func (r *queryTaskHandler) Handle(request schttp.HttpRequest) schttp.HttpResponse {
	response := &define.QueryTaskRsp{}
	response.RetCode = schttp.OK
	switch request.Method {
	case schttp.MethodPost:
		handleQueryTask(request, response)
	default:
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("http请求方法错误:%s", request.Method)
	}
	return schttp.HttpResponse{Response: response}
}

func handleQueryTask(request schttp.HttpRequest, response *define.QueryTaskRsp) {
	var queryTaskReq define.QueryTaskReq
	if err := json.Unmarshal([]byte(request.Body), &queryTaskReq); err != nil {
		response.RetCode = schttp.BadRequest
		response.Message = fmt.Sprintf("unmarshal json error: %s", err.Error())
		return
	}

	if err := validate.Validate(queryTaskReq); err != nil {
		response.RetCode = schttp.BadRequest
		response.Message = err.Error()
		return
	}
	convertTask, ok := schedulertask.GetConvertTaskByID(queryTaskReq.TaskID)
	if !ok {
		response.RetCode = schttp.NotFound
		response.Message = "转模任务不存在"
		return
	}
	response.Data = convertTask
}
