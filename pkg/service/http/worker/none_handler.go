package worker

import (
	"log"

	schttp "git.code.oa.com/RondaServing/ServingController/http"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

func init() {
	path := "/none"
	if err := schttp.RegisterHandler(path, &noneHandler{}, schttp.Json); err != nil {
		log.Fatalf("Register api handler:%s error:%s", path, err.Error())
	}
}

type noneHandler struct {
}

func (g noneHandler) Handle(httpRequest schttp.HttpRequest) schttp.HttpResponse {
	response := &define.Response{}
	response.RetCode = schttp.OK
	return schttp.HttpResponse{Response: response}
}
