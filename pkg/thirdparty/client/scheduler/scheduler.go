/*
Package scheduler convert_scheduler接口调用客户端
使用Factory，传入Config配置，初始化Client

client中应该只提供调用功能，不要在这里组合逻辑，也不应该包含工程内的其他包
*/
package scheduler

import (
	"context"
	"fmt"
	"time"

	tClient "git.code.oa.com/trpc-go/trpc-go/client"
	tCodec "git.code.oa.com/trpc-go/trpc-go/codec"
	tHttp "git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"

	workerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/worker"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

// Client ops调用客户端
type Client struct {
	config     workerconfig.SchedulerConfig
	httpClient tHttp.Client
}

// Factory Client工厂，必须使用工厂创造client进行初始化
func Factory(clientConfig workerconfig.SchedulerConfig) *Client {
	log.Infof("Initialize ConvertScheduler client, config=%+v.", clientConfig)

	headerMap := make(map[string]string)
	headerMap["Content-Type"] = "application/json"
	return &Client{
		config: clientConfig,
		httpClient: tHttp.NewClientProxy(
			clientConfig.ServiceName,
			tClient.WithSerializationType(tCodec.SerializationTypeJSON),
			tClient.WithTimeout(10*time.Second),
		),
	}
}

// GetConvertTasks 查询转模任务
func (c *Client) GetConvertTasks(req define.GetTasksReq) (define.GetTasksRsp, error) {
	rsp := define.GetTasksRsp{}
	err := c.httpClient.Post(context.Background(), define.UrlGetTasks, req, &rsp)
	if err != nil {
		return rsp, fmt.Errorf("query convert tasks failed: %s", err.Error())
	}
	return rsp, nil
}

// ReportConvertTask 上报转模任务状态
func (c *Client) ReportConvertTask(req define.ReportTasksReq) (define.ReportTasksRsp, error) {
	rsp := define.ReportTasksRsp{}
	err := c.httpClient.Post(context.Background(), define.UrlReportTasks, req, &rsp)
	if err != nil {
		return rsp, fmt.Errorf("update convert task, err=%s", err.Error())
	}
	return rsp, nil
}
