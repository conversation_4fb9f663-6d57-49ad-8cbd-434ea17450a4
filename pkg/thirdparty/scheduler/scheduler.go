// Package scheduler 对client scheduler包的操作封装
package scheduler

import (
	"context"
	"errors"
	"fmt"
	"time"

	"git.code.oa.com/trpc-go/trpc-go/client"
	"git.code.oa.com/trpc-go/trpc-go/codec"
	"git.code.oa.com/trpc-go/trpc-go/http"
	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/commvar"
	workerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/worker"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

// trpc tdmq client服务初始化
var ConvertShedulerClientProxy *HttpClient

// Initialize 初始化
func Initialize() {
	serviceName := workerconfig.GetConfig().ThirdParty.Scheduler.ServiceName
	ConvertShedulerClientProxy = newHttpClient(serviceName)
}

// GetConvertTasks 获取转模任务记录
func GetConvertTasks() ([]define.ConvertTask, error) {
	req := define.GetTasksReq{
		WorkerBaseInfo: define.WorkerBaseInfo{
			ContainerName: commvar.GetCommVar().ContainerName,
			SetName:       commvar.GetCommVar().FullSetName,
		},
	}
	rsp, err := ConvertShedulerClientProxy.GetConvertTasks(req)
	log.Debugf("GetConvertTasks, rsp:%+v", rsp)
	if err != nil {
		log.Errorf("GetConvertTasks failed,err:%s", err.Error())
		return nil, err
	}
	if rsp.RetCode != 0 {
		log.Errorf("GetConvertTasks failed, rsp:%+v", rsp)
		return nil, errors.New(rsp.Message)
	}
	return rsp.Data, nil
}

// ReportConvertTasksResult 上报转模任务结果
func ReportConvertTasksResult(reportTasks []define.ReportTask) error {
	req := define.ReportTasksReq{Tasks: reportTasks}
	rsp, err := ConvertShedulerClientProxy.ReportConvertTask(req)
	log.Debugf("req:%+v, rsp:%+v", req, rsp)
	if err != nil {
		log.Errorf("ReportConvertTasksResult failed,err:%s", err.Error())
		return err
	}
	if rsp.RetCode != 0 {
		log.Errorf("ReportConvertTasksResult failed, rsp:%+v", rsp)
		return errors.New(rsp.Message)
	}
	return nil
}

// HttpClient ConvertScheduler http调用客户端
type HttpClient struct {
	httpClient http.Client
}

func newHttpClient(serviceName string) *HttpClient {
	headerMap := make(map[string]string)
	headerMap["Content-Type"] = "application/json"
	return &HttpClient{
		httpClient: http.NewClientProxy(
			serviceName,
			client.WithSerializationType(codec.SerializationTypeJSON),
			client.WithTimeout(10*time.Second),
		),
	}
}

// GetConvertTasks 查询转模任务
func (c *HttpClient) GetConvertTasks(req define.GetTasksReq) (define.GetTasksRsp, error) {
	rsp := define.GetTasksRsp{}
	err := c.httpClient.Post(context.Background(), define.UrlNewGetTasks, req, &rsp)
	if err != nil {
		return rsp, fmt.Errorf("query convert tasks failed: %s", err.Error())
	}
	return rsp, nil
}

// ReportConvertTask 上报转模任务状态
func (c *HttpClient) ReportConvertTask(req define.ReportTasksReq) (define.ReportTasksRsp, error) {
	rsp := define.ReportTasksRsp{}
	err := c.httpClient.Post(context.Background(), define.UrlNewReportTasks, req, &rsp)
	if err != nil {
		return rsp, fmt.Errorf("update convert task, err=%s", err.Error())
	}
	return rsp, nil
}
