// Package scheduler 对client scheduler包的操作封装
package scheduler

import (
	"errors"

	"git.code.oa.com/trpc-go/trpc-go/log"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/commvar"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/thirdparty"
)

// GetConvertTasks 获取转模任务记录
func GetConvertTasks() ([]define.ConvertTask, error) {
	client := thirdparty.GetProxyManager().ConvertShedulerClientProxy
	req := define.GetTasksReq{
		WorkerBaseInfo: define.WorkerBaseInfo{
			ContainerName: commvar.GetCommVar().ContainerName,
			SetName:       commvar.GetCommVar().FullSetName,
		},
	}
	rsp, err := client.GetConvertTasks(req)
	log.Debugf("GetConvertTasks, rsp:%+v", rsp)
	if err != nil {
		log.Errorf("GetConvertTasks failed,err:%s", err.Error())
		return nil, err
	}
	if rsp.RetCode != 0 {
		log.Errorf("GetConvertTasks failed, rsp:%+v", rsp)
		return nil, errors.New(rsp.Message)
	}
	return rsp.Data, nil
}

// ReportConvertTasksResult 上报转模任务结果
func ReportConvertTasksResult(reportTasks []define.ReportTask) error {
	client := thirdparty.GetProxyManager().ConvertShedulerClientProxy
	req := define.ReportTasksReq{Tasks: reportTasks}
	rsp, err := client.ReportConvertTask(req)
	log.Debugf("req:%+v, rsp:%+v", req, rsp)
	if err != nil {
		log.Errorf("ReportConvertTasksResult failed,err:%s", err.Error())
		return err
	}
	if rsp.RetCode != 0 {
		log.Errorf("ReportConvertTasksResult failed, rsp:%+v", rsp)
		return errors.New(rsp.Message)
	}
	return nil
}
