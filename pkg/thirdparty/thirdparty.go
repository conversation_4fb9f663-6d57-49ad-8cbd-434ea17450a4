// Package thirdparty 各个第三方模块的初始化入口
package thirdparty

import (
	"sync"

	workerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/worker"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/thirdparty/client/scheduler"
)

// trpc tdmq client服务初始化
var proxyManager *ProxyManager
var once sync.Once

// ProxyManager 统一实例化
type ProxyManager struct {
	ConvertShedulerClientProxy *scheduler.Client
}

// GetProxyManager proxy manager singleton 实现
func GetProxyManager() *ProxyManager {
	once.Do(func() {
		proxyManager = &ProxyManager{}
		proxyManager.init()
	})
	return proxyManager
}

// init 初始化内部 proxy 代理
func (pm *ProxyManager) init() {
	thirdPartyConfig := workerconfig.GetConfig().ThirdParty
	// 初始化ConvertShedulerClientProxy
	pm.ConvertShedulerClientProxy = scheduler.Factory(thirdPartyConfig.Scheduler)
}

// Initialize 初始化
func Initialize() {
}
