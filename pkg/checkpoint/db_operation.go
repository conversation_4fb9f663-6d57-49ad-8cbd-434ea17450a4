package checkpoint

import (
	"database/sql"
	"fmt"
	"reflect"
	"strings"

	"git.code.oa.com/trpc-go/trpc-go/log"
	"github.com/jmoiron/sqlx"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/monitor"
	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/monitor/monreporter"
)

func dbExec(db *sqlx.DB, sql string, args ...interface{}) (sql.Result, error) {
	ret, err := db.Exec(sql, args...)
	if err != nil {
		log.Errorf("dbExec failed,sql:[%s] args:[%+v] err:[%s]", sql, args, err.Error())
		monitor.ReportCustomWarning(monreporter.CustomWarning{
			Level:   monreporter.Urgent,
			Title:   "数据库操作失败",
			Content: fmt.Sprintf("sql:[%s] args:[%+v]", sql, args),
		})
	} else {
		rows, _ := ret.RowsAffected()
		log.Debugf("sql:[%s] args:[%+v] rows:[%d]", sql, args, rows)
	}
	return ret, err
}

func dbNamedExec(db *sqlx.DB, sql string, arg interface{}) (sql.Result, error) {
	ret, err := db.NamedExec(sql, arg)
	if err != nil {
		log.Errorf("dbNamedExec failed,sql:[%s] arg:[%+v] err:[%s]", sql, arg, err.Error())
		monitor.ReportCustomWarning(monreporter.CustomWarning{
			Level:   monreporter.Urgent,
			Title:   "数据库操作失败",
			Content: fmt.Sprintf("sql:%s arg:%+v", sql, arg),
		})
	} else {
		ret.LastInsertId()
		rows, _ := ret.RowsAffected()
		log.Debugf("sql:[%s] arg:[%+v] rows:[%d]", sql, arg, rows)
	}
	return ret, err
}

// replaceSQLStmt 生成replace的sql执行语句
func replaceSQLStmt(table string, kvs map[string]interface{}) (string, []interface{}, error) {
	keys := make([]string, 0, len(kvs))
	values := make([]interface{}, 0, len(kvs))
	for k, v := range kvs {
		keys = append(keys, k)
		values = append(values, v)
	}
	sqlStr := fmt.Sprintf("REPLACE INTO %s (%s) VALUES (%s)",
		table, strings.Join(keys, ", "), "?"+strings.Repeat(", ?", len(kvs)-1))
	return sqlStr, values, nil
}

// SQLReplace 执行replace的sql语句
func SQLReplace(db *sqlx.DB, table string, kvs map[string]interface{}) (sql.Result, error) {
	if len(kvs) == 0 {
		return nil, fmt.Errorf("kvs is nil")
	}
	sqlStr, values, err := replaceSQLStmt(table, kvs)
	if err != nil {
		return nil, err
	}
	return dbExec(db, sqlStr, values...)
}

// updateSQLStmt 生成update的sql执行语句
func updateSQLStmt(
	table string,
	conditions map[string]interface{},
	kvs map[string]interface{}) (string, []interface{}, error) {

	values := make([]interface{}, 0, len(kvs))
	setKeys := make([]string, 0, len(kvs))
	for key, val := range kvs {
		if _, ok := conditions[key]; !ok {
			setKeys = append(setKeys, fmt.Sprintf("%s = ?", key))
			values = append(values, val)
		}
	}
	condKeys := make([]string, 0, len(conditions))
	for key, val := range conditions {
		condKeys = append(condKeys, fmt.Sprintf("%s = ?", key))
		values = append(values, val)
	}
	sqlStr := fmt.Sprintf("UPDATE %s SET %s WHERE %s",
		table, strings.Join(setKeys, ","), strings.Join(condKeys, " AND "))
	return sqlStr, values, nil
}

// SQLUpdate 执行update的sql语句
func SQLUpdate(
	db *sqlx.DB,
	table string,
	conditions map[string]interface{},
	kvs map[string]interface{}) (sql.Result, error) {

	if len(kvs) == 0 || len(conditions) == 0 {
		return nil, fmt.Errorf("no input or condtion kvs")
	}
	sqlStr, values, err := updateSQLStmt(table, conditions, kvs)
	if err != nil {
		return nil, err
	}
	return dbExec(db, sqlStr, values...)
}

// selectSQLStmt 生成select的sql执行语句
func selectSQLStmt(
	table string,
	cols []string,
	conditions map[string]interface{}) (string, []interface{}, error) {

	var sqlStr string
	var values = make([]interface{}, 0, len(conditions))
	if len(conditions) == 0 {
		sqlStr = fmt.Sprintf("SELECT %s FROM %s", strings.Join(cols, ", "), table)
	} else {
		condKeys := make([]string, 0, len(conditions))
		for key, val := range conditions {
			condKeys = append(condKeys, fmt.Sprintf("%s = ?", key))
			values = append(values, val)
		}
		sqlStr = fmt.Sprintf("SELECT %s FROM %s WHERE %s",
			strings.Join(cols, ", "), table, strings.Join(condKeys, " AND "))
	}
	return sqlStr, values, nil
}

// SQLSelect 执行select的sql语句
func SQLSelect(
	db *sqlx.DB,
	table string,
	cols []string,
	conditions map[string]interface{}) (*sqlx.Rows, error) {

	if len(cols) == 0 {
		return nil, fmt.Errorf("no select keys")
	}
	sqlStr, values, err := selectSQLStmt(table, cols, conditions)
	if err != nil {
		return nil, err
	}
	ret, err := db.Queryx(sqlStr, values...)
	if err != nil {
		log.Errorf("db.Query failed, err=%+v, sqlStr=%s\n", err, sqlStr)
		return nil, err
	}
	return ret, nil
}

// deleteSQLStmt 生成delete的sql执行语句
func deleteSQLStmt(table string, conditions map[string]interface{}) (string, []interface{}, error) {
	var sqlStr string
	var values = make([]interface{}, 0, len(conditions))
	condKeys := make([]string, 0, len(conditions))
	for key, val := range conditions {
		condKeys = append(condKeys, fmt.Sprintf("%s = ?", key))
		values = append(values, val)
	}
	sqlStr = fmt.Sprintf("DELETE FROM %s WHERE %s", table, strings.Join(condKeys, " AND "))
	return sqlStr, values, nil
}

// SQLDelete 执行delete的sql语句
func SQLDelete(db *sqlx.DB, table string, conditions map[string]interface{}) (sql.Result, error) {
	if len(conditions) == 0 {
		return nil, fmt.Errorf("SQLDelete condition is nil")
	}
	sqlStr, values, err := deleteSQLStmt(table, conditions)
	if err != nil {
		return nil, err
	}
	return dbExec(db, sqlStr, values...)
}

// replaceStructStmt 生成replace的sql执行语句,传入struct传入方式
func replaceStructStmt(table string, obj interface{}) (string, error) {
	fields, err := getDbFields(obj)
	if err != nil {
		return "", err
	}
	fieldList := strings.Join(fields, ", ")
	for i, val := range fields {
		fields[i] = fmt.Sprintf(":%s", val)
	}
	namedList := strings.Join(fields, ", ")
	sqlStr := fmt.Sprintf("REPLACE INTO %s (%s) VALUES (%s) ", table, fieldList, namedList)
	return sqlStr, nil
}

// StructReplace 执行replace语句,使用struct传入方式
func StructReplace(db *sqlx.DB, tab string, obj interface{}) (sql.Result, error) {
	sqlStr, err := replaceStructStmt(tab, obj)
	if err != nil {
		return nil, err
	}
	return dbNamedExec(db, sqlStr, obj)
}

// updateStructStmt 生成update的sql执行语句,传入struct传入方式
func updateStructStmt(table string, obj interface{}, keys map[string]struct{}) (string, error) {
	fields, err := getDbFields(obj)
	if err != nil {
		return "", err
	}
	var setFields []string
	var whereKeys []string
	for _, val := range fields {
		// 在keys里面的,不需要写入到sql语句中的set里面去,因为他们本身就是where的条件
		if _, ok := keys[val]; !ok {
			setFields = append(setFields, fmt.Sprintf("%s=:%s", val, val))
		}
	}
	for key := range keys {
		whereKeys = append(whereKeys, fmt.Sprintf("%s=:%s", key, key))
	}
	sqlStr := fmt.Sprintf("UPDATE %s SET %s WHERE %s ",
		table, strings.Join(setFields, ", "), strings.Join(whereKeys, " AND "))
	return sqlStr, nil
}

// StructUpdate 执行update语句,使用struct传入方式
func StructUpdate(
	db *sqlx.DB,
	table string,
	obj interface{},
	keys map[string]struct{}) (sql.Result, error) {

	if len(keys) == 0 {
		return nil, fmt.Errorf("StructUpdate keys is nil")
	}
	sqlStr, err := updateStructStmt(table, obj, keys)
	if err != nil {
		return nil, err
	}
	return dbNamedExec(db, sqlStr, obj)
}

// selectStructStmt 生成select的sql执行语句,传入struct传入方式
func selectStructStmt(
	table string,
	obj interface{},
	conditions map[string]interface{},
	limits []string) (string, []interface{}, error) {

	fields, err := getDbFields(obj)
	if err != nil {
		return "", nil, err
	}
	var sqlStr string
	var whereStr string
	var values = make([]interface{}, 0, len(conditions))
	if len(conditions) > 0 {
		condKeys := make([]string, 0, len(conditions))
		for key, val := range conditions {
			condKeys = append(condKeys, fmt.Sprintf("%s = ?", key))
			values = append(values, val)
		}
		whereStr = " WHERE " + strings.Join(condKeys, " AND ")
	}
	// 最后的order by / limit offset等语句
	var limitStr string
	if len(limits) > 0 {
		limitStr = strings.Join(limits, " ")
	}
	sqlStr = fmt.Sprintf("SELECT %s FROM %s %s %s",
		strings.Join(fields, ","), table, whereStr, limitStr)
	return sqlStr, values, nil
}

// StructSelect 执行select语句,使用struct传入方式
func StructSelect(
	db *sqlx.DB,
	table string,
	obj interface{},
	conditions map[string]interface{},
	limit []string) (*sqlx.Rows, error) {

	sqlStr, values, err := selectStructStmt(table, obj, conditions, limit)
	if err != nil {
		return nil, err
	}
	ret, err := db.Queryx(sqlStr, values...)
	if err != nil {
		log.Errorf("db.Query failed, err=%+v, sql=%s\n", err, sqlStr)
		return nil, err
	}
	return ret, nil
}

// getDbFields 获取结构体db tag的值
func getDbFields(obj interface{}) ([]string, error) {
	valueOf := reflect.ValueOf(obj)
	if valueOf.Kind() == reflect.Ptr {
		valueOf = valueOf.Elem()
	}
	var fields []string
	if valueOf.Kind() == reflect.Struct {
		for i := 0; i < valueOf.NumField(); i++ {
			field := valueOf.Type().Field(i).Tag.Get("db")
			if len(field) > 0 && field != "-" {
				if field != ",inline" {
					fields = append(fields, field)
				} else {
					// 当 tag 是 ",inline" 时，递归获取内嵌结构体的字段
					embeddedValue := valueOf.Field(i)
					if embeddedValue.Kind() == reflect.Ptr {
						embeddedValue = embeddedValue.Elem()
					}
					if embeddedValue.IsValid() && embeddedValue.Kind() == reflect.Struct {
						embeddedFields, err := getDbFields(embeddedValue.Interface())
						if err != nil {
							return nil, err
						}
						fields = append(fields, embeddedFields...)
					}
				}
			}
		}
		return fields, nil
	}
	return nil, fmt.Errorf("requires struct or map, found: %s", valueOf.Kind().String())
}
