// Package checkpoint 实现了数据库增删改查的一些操作封装
package checkpoint

import (
	"fmt"

	"git.code.oa.com/trpc-go/trpc-go/log"
	_ "github.com/go-sql-driver/mysql"
	"github.com/jmoiron/sqlx"
	_ "github.com/mattn/go-sqlite3"

	schedulerconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/scheduler"
)

const (
	// DriverSQLite sqlite3 数据库(用于测试)
	DriverSQLite = "sqlite3"
	// DriverMySQL mysql 数据库
	DriverMySQL = "mysql"
)

var (
	dbHandler    *sqlx.DB
	dbDriverName = DriverSQLite
)

// Initialize 初始化
func Initialize() {
	log.Info("Init checkpoint")
	dbConf := DefaultConfig()
	conf := schedulerconfig.GetConfig()
	if conf.Database.Dbname != "" {
		dbConf.DriverName = DriverMySQL
		source := fmt.Sprintf("%s:%s@(%s:%s)/%s?charset=%s&parseTime=True&loc=Local",
			conf.Database.Username,
			conf.Database.Password,
			conf.Database.Host,
			conf.Database.Port,
			conf.Database.Dbname,
			conf.Database.Charset)
		dbConf.DataSource = source
		dbConf.MaxOpenConns = conf.Database.MaxOpenConns
		dbConf.MaxIdleCons = conf.Database.MaxIdleCons
	}
	dbDriverName = dbConf.DriverName
	handler, err := connectDatabase(dbConf)
	if handler == nil {
		log.Fatal("Init database failed:handler is nil")
	}
	if err != nil {
		log.Fatalf("Init database failed:%s", err.Error())
	}
	dbHandler = handler
}

// UnInitialize 反初始化
func UnInitialize() {
	if dbHandler != nil {
		dbHandler.Close()
		dbHandler = nil
	}
}

// GetDBHandler 获取DB句柄
func GetDBHandler() *sqlx.DB {
	return dbHandler
}

// GetDBDriverName 获取当前使用DB类型
func GetDBDriverName() string {
	return dbDriverName
}

// DatabaseConfig 数据库配置结构
type DatabaseConfig struct {
	DriverName   string
	DataSource   string
	MaxOpenConns int
	MaxIdleCons  int
}

// DefaultConfig 默认数据库配置
func DefaultConfig() DatabaseConfig {
	return DatabaseConfig{
		DriverName: DriverSQLite,
		// DataSource: "/tmp/serving_controller.sql?_loc=Local",
		DataSource:   ":memory:?_loc=Local",
		MaxOpenConns: 100,
		MaxIdleCons:  20,
	}
}

// connectDatabase 数据库进行连接
func connectDatabase(ckptConfig DatabaseConfig) (*sqlx.DB, error) {
	switch ckptConfig.DriverName {
	case DriverMySQL, DriverSQLite:
		db, err := sqlx.Connect(ckptConfig.DriverName, ckptConfig.DataSource)
		if db != nil && err == nil {
			db.SetMaxOpenConns(ckptConfig.MaxOpenConns)
			db.SetMaxIdleConns(ckptConfig.MaxIdleCons)
		}
		return db, err
	default:
		return nil, fmt.Errorf("unsuported driver %s", ckptConfig.DriverName)
	}
}
