// Package worker 实现了配置相关功能,包括从七彩石读取,全局变量,日志控制变量等
package worker

import (
	"context"
	"fmt"
	"sync/atomic"

	tc "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gopkg.in/yaml.v3"

	commonconfig "git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/config/common"
)

const (
	// 主板七彩石
	providerRainbowTconf = "rainbow_tconf"
	// configFileName 配置文件名
	configFileName = "config.yaml"
)

var (
	// gloConf 全局配置,读取rainbow
	gloConf atomic.Value
)

func init() {
	gloConf.Store(&Config{})
}

// Initialize  初始化
func Initialize() {
	log.Info("Init config")
	// 初始化主板七彩石配置
	conf := &Config{}
	err := tc.GetYAML(configFileName, conf)
	if err != nil {
		log.Fatalf("GetYAML error:%s", err.Error())
	}
	// 保存配置
	setConfig(conf)
	// 打印config的yaml
	logConfigWithYaml(conf)
	// 监听配置变更
	watchConfig()
}

// Config 服务的配置,注意每增加一项要在 setConfig 检查并设置其默认值
type Config struct {
	ThirdParty thirdPartyConf `yaml:"thirdparty"` // 第三方相关配置
	Cls        clsConf        `yaml:"cls"`
	Errors     errorsConfig   `yaml:"errors"` // 错误信息配置
}

type errorsConfig []struct {
	ErrMsg string `yaml:"errMsg"` // 包含的字符串
	Code   string `yaml:"code"`
	Reason string `yaml:"reason"` // 展示到页面上的提示
}

type clsConf struct {
	Endpoint        string `yaml:"endpoint"`
	AccessKeyID     string `yaml:"accessKeyID"`
	AccessKeySecret string `yaml:"accessKeySecret"`
	TopicID         string `yaml:"topicID"`
}

type thirdPartyConf struct {
	Scheduler SchedulerConfig `yaml:"scheduler"`
	OpenAPI   openAPIConf     `yaml:"openapi"`
}

// SchedulerConfig convert_manager调用客户端配置信息
type SchedulerConfig struct {
	ServiceName string `yaml:"serviceName"`
}

type openAPIConf struct {
	SecretID  string `yaml:"secretID"`
	SecretKey string `yaml:"secretKey"`
}

// watchConfig 监听配置
func watchConfig() {
	rainbowTconfCH := getWatchCH(providerRainbowTconf)
	go func() {
		for {
			select {
			case data, ok := <-rainbowTconfCH:
				if !ok {
					// TODO(benshen):告警
					log.Infof("rainbowMainCH closed")
					break
				}
				if newConf, ok := unmarshalConfig(data); ok {
					setConfig(newConf)
					logConfigWithYaml(newConf)
				}
			}
		}
	}()
}

// unmarshalConfig 解析配置
func unmarshalConfig(data tc.Response) (*Config, bool) {
	if data.Event() != tc.EventTypePut {
		return nil, false
	}
	conf := &Config{}
	log.Infof("Recv config update:%d\n%s", data.Event(), data.Value())
	if err := yaml.Unmarshal([]byte(data.Value()), conf); err != nil {
		log.Errorf("Unmarshal yaml failed:%s", err.Error())
		return nil, false
	}
	return conf, true
}

func getWatchCH(provider string) <-chan tc.Response {
	kvConfig := tc.Get(provider)
	if kvConfig == nil {
		log.Fatalf("trpc config get error, return a null kv config")
	}
	watchCH, err := tc.Get(provider).Watch(context.TODO(), configFileName)
	if err != nil {
		log.Fatalf("Watch config:%s error:%s", configFileName, err.Error())
	}
	return watchCH
}

// GetConfig 获取全局的配置,调用的时候要注意配置会被 Watch,即有可能中途改变,不允许改变的要另外保存
func GetConfig() *Config {
	conf := gloConf.Load().(*Config)
	return conf
}

// setConfig 设置配置变量入口
func setConfig(conf *Config) {
	// 检查参数,设置最小默认值
	checkConfig(conf)
	gloConf.Store(conf)
}

func checkThirdParty(conf *Config) error {
	if conf.ThirdParty.Scheduler.ServiceName == "" {
		conf.ThirdParty.Scheduler.ServiceName = "trpc.ServingPaas.ConvertScheduler.Http"
	}
	return nil
}

func checkClsConf(conf *Config) error {
	emptyFileName := commonconfig.ValidateRequiredFields(conf.Cls, "clsConf")
	if len(emptyFileName) > 0 {
		return fmt.Errorf("clsConf exists some empty filed:%+v", emptyFileName)
	}
	return nil
}

// checkConfig 检查配置合法性
func checkConfig(conf *Config) {
	log.Infof("Before check:%+v", conf)
	if err := checkThirdParty(conf); err != nil {
		log.Fatalf("checkThirdParty failed:%s", err.Error())
	}
	if err := checkClsConf(conf); err != nil {
		log.Fatalf("checkClsConf failed:%s", err.Error())
	}
	log.Infof("Config after set:%+v", conf)
}

func logConfigWithYaml(conf *Config) {
	// 用yaml的格式打印出来
	if yamlData, err := yaml.Marshal(conf); err != nil {
		log.Errorf("Failed to marshal config to YAML: %v", err)
	} else {
		log.Infof("Config after set:\n%s", string(yamlData))
	}
}
