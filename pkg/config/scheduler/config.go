// Package scheduler 实现了配置相关功能,包括从七彩石读取,全局变量,日志控制变量等
package scheduler

import (
	"context"
	"sync/atomic"

	trpcconf "git.code.oa.com/trpc-go/trpc-go/config"
	"git.code.oa.com/trpc-go/trpc-go/log"
	"gopkg.in/yaml.v3"

	"git.woa.com/RondaServing/ServingControllerProjects/ConvertController/pkg/define"
)

const (
	// 主板七彩石
	providerRainbowTconf = "rainbow_tconf"
	// configFileName 配置文件名
	configFileName = "config.yaml"
)

var (
	// gloConf 全局配置,读取rainbow
	gloConf atomic.Value
)

func init() {
	setConfig(&Config{})
}

type databaseConfig struct {
	// ----Mysql 数据库连接必须字段
	Username string `yaml:"username"`
	Password string `yaml:"password"`
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	Dbname   string `yaml:"dbname"`
	Charset  string `yaml:"charset"`
	// ----数据库连接的一些配置
	// MaxOpenConns 最大打开连接数
	MaxOpenConns int `yaml:"maxOpenConns"`
	// MaxIdleCons 最大空闲连接数
	MaxIdleCons int `yaml:"maxIdleCons"`
}

// MasterSlaveConfig 服务主备配置
type MasterSlaveConfig struct {
	// IsMasterSlave 是否启用主备
	IsMasterSlave bool `yaml:"isMasterSlave"`
	// LockKey 主备抢锁key, 正式环境：/convert/formal/lock/, 测试环境：/convert/test/lock/
	LockKeyPrefix string `yaml:"lockKeyPrefix"`
	// ETCDUserName etcd用户名
	ETCDUserName string `yaml:"etcdUserName"`
	// ETCDPasswd etcd密码
	ETCDPasswd string `yaml:"etcdPasswd"`
	// ETCDAddr etcd地址
	ETCDAddr string `yaml:"etcdAddr"`
	// KeepAliveInterval 保活超时时间
	KeepAliveIterval int64 `yaml:"keepAliveInterval"`
}

type heartbeatCheck struct {
	// CheckInterval 检查间隔,单位秒
	CheckInterval int `yaml:"checkInterval"`
	// StartupCheckInterval 启动检查间隔,单位秒
	StartupCheckInterval int `yaml:"startupCheckInterval"`
	// WorkerTimeout 节点心跳超时时间,单位秒
	WorkerTimeout int `yaml:"workerTimeout"`
	// DealTimeoutWorkersOnce 每次处理超时节点数量
	DealTimeoutWorkersOnce int `yaml:"dealTimeoutWorkersOnce"`
}

type heartbeatReport struct {
	// Interval 上报最小间隔,单位为秒
	Interval int `yaml:"interval"`
	// QueueSize 每个上报队列大小
	QueueSize int `yaml:"queueSize"`
	// QueueNum 上报队列数量
	QueueNum int `yaml:"queueNum"`
}

type heartbeatConfig struct {
	// Check 检查心跳是否超时
	Check heartbeatCheck `yaml:"check"`
	// Report 上报心跳
	Report heartbeatReport `yaml:"report"`
}

type WorkerSet struct {
	GPUType             string                         `yaml:"gpuType"`             // GPU类型
	Image               string                         `yaml:"image"`               // 镜像
	SupportConvertTypes []define.ConvertType           `yaml:"supportConvertTypes"` // 支持的转模类型
	SupportEngineInfo   map[define.EngineType][]string `yaml:"supportEngineInfo"`   // 支持的引擎类型及版本,key:engineType value:version
	MinWorkers          int                            `yaml:"minWorkers"`          // 最小Worker数量
	MaxWorkers          int                            `yaml:"maxWorkers"`          // 最大Worker数量
	IsPrivate           bool                           `yaml:"isPrivate"`           // 是否是专用set
	AllowToPublic       bool                           `yaml:"allowToPublic"`       // Set内的Worker无空闲时,是否允许将任务分配到公共Set
	WaitDelaySecs       int                            `yaml:"waitDelaySecs"`       // 等待延迟秒数(AllowToPublic=true时有效)
	AppGroupIDs         []string                       `yaml:"appGroupIDs"`         // 匹配应用组ID(IsPrivate=true时有效)
	ServerIDs           []int                          `yaml:"serverIDs"`           // 匹配服务ID(IsPrivate=true时有效)
	ModelNames          []string                       `yaml:"modelNames"`          // 匹配模型名(IsPrivate=true时有效)
}

type WorkerSetConfig struct {
	WorkerService string               `yaml:"workerService"`
	Sets          map[string]WorkerSet `yaml:"sets"`
}

type JobConfig struct {
	Cleanup struct {
		CleanConvertTaskInterval int `yaml:"cleanConvertTaskInterval"` // 清理转模任务间隔,单位秒
		ConvertTaskExpireTime    int `yaml:"convertTaskExpireTime"`    // 转模任务过期时间,单位秒
	} `yaml:"cleanup"`
}

type TaskPriorityRule struct {
	Name             string   `yaml:"name"` // 无其他作用,作为规则的说明
	AppGroupIDs      []string `yaml:"appGroupIDs"`
	ServerIDs        []int    `yaml:"serverIDs"`
	OriginModelNames []string `yaml:"originModelNames"`
	Priority         uint     `yaml:"priority"`
}

type taskPriorityConfig struct {
	DefaultPriority uint               `yaml:"defaultPriority"` // DefaultPriority 默认优先级
	Rules           []TaskPriorityRule `yaml:"rules"`           // Rules 优先级规则
}

// Config 服务的配置,注意每增加一项要在 setConfig 检查并设置其默认值
type Config struct {
	Database          databaseConfig     `yaml:"database"`          // 数据库配置
	MasterSlaveConfig MasterSlaveConfig  `yaml:"masterSlaveConfig"` // 主备配置
	Heartbeat         heartbeatConfig    `yaml:"heartbeat"`         // 心跳配置
	WorkerSetConf     WorkerSetConfig    `yaml:"workerSet"`         // worker集群配置
	Job               JobConfig          `yaml:"job"`               // 定时任务配置
	TaskPriority      taskPriorityConfig `yaml:"taskPriority"`      // 任务优先级配置
}

// Initialize  初始化
func Initialize() {
	log.Info("Init config")
	// 初始化主板七彩石配置
	conf := &Config{}
	err := trpcconf.GetYAML(configFileName, conf)
	if err != nil {
		log.Fatalf("GetYAML error:%s", err.Error())
	}
	// 保存配置
	setConfig(conf)
	// 打印config的yaml
	logConfigWithYaml(conf)
	// 监听配置变更
	watchConfig()
}

// watchConfig 监听配置
func watchConfig() {
	rainbowTconfCH := getWatchCH(providerRainbowTconf)
	go func() {
		for {
			select {
			case data, ok := <-rainbowTconfCH:
				if !ok {
					// TODO(benshen):告警
					log.Infof("rainbowMainCH closed")
					break
				}
				if newConf, ok := unmarshalConfig(data); ok {
					setConfig(newConf)
					logConfigWithYaml(newConf)
				}
			}
		}
	}()
}

// unmarshalConfig 解析配置
func unmarshalConfig(data trpcconf.Response) (*Config, bool) {
	if data.Event() != trpcconf.EventTypePut {
		return nil, false
	}
	conf := &Config{}
	log.Infof("Recv config update:%d\n%s", data.Event(), data.Value())
	if err := yaml.Unmarshal([]byte(data.Value()), conf); err != nil {
		log.Errorf("Unmarshal yaml failed:%s", err.Error())
		return nil, false
	}
	return conf, true
}

func getWatchCH(provider string) <-chan trpcconf.Response {
	kvConfig := trpcconf.Get(provider)
	if kvConfig == nil {
		log.Fatalf("trpc config get error, return a null kv config")
	}
	watchCH, err := trpcconf.Get(provider).Watch(context.TODO(), configFileName)
	if err != nil {
		log.Fatalf("Watch config:%s error:%s", configFileName, err.Error())
	}
	return watchCH
}

// GetConfig 获取全局的配置,调用的时候要注意配置会被 Watch,即有可能中途改变,不允许改变的要另外保存
func GetConfig() *Config {
	conf := gloConf.Load().(*Config)
	return conf
}

func checkConfigDatabase(conf *Config) {
	if conf.Database.MaxOpenConns == 0 {
		conf.Database.MaxOpenConns = 100
	}
	if conf.Database.MaxIdleCons == 0 {
		conf.Database.MaxIdleCons = 20
	}
}

func checkConfigHeartbeat(conf *Config) {
	const defCheckInterval = 30
	if conf.Heartbeat.Check.CheckInterval < defCheckInterval {
		conf.Heartbeat.Check.CheckInterval = defCheckInterval
	}
	const defStartupCheckInterval = 5 * 60
	if conf.Heartbeat.Check.StartupCheckInterval < defStartupCheckInterval {
		conf.Heartbeat.Check.StartupCheckInterval = defStartupCheckInterval
	}
	const defWorkerTimeout = 6 * 60
	if conf.Heartbeat.Check.WorkerTimeout < defWorkerTimeout {
		conf.Heartbeat.Check.WorkerTimeout = defWorkerTimeout
	}
	const defDealTimeoutWorkersOnce = 5
	if conf.Heartbeat.Check.DealTimeoutWorkersOnce < defDealTimeoutWorkersOnce {
		conf.Heartbeat.Check.DealTimeoutWorkersOnce = defDealTimeoutWorkersOnce
	}
	const defReportInterval = 30
	if conf.Heartbeat.Report.Interval < defReportInterval {
		conf.Heartbeat.Report.Interval = defReportInterval
	}
	const defQueueSize = 1000
	if conf.Heartbeat.Report.QueueSize < defQueueSize {
		conf.Heartbeat.Report.QueueSize = defQueueSize
	}
	const defQueueNum = 5
	if conf.Heartbeat.Report.QueueNum < defQueueNum {
		conf.Heartbeat.Report.QueueNum = defQueueNum
	}
}

func checkConfigWorkerSet(conf *Config) {
	if conf.WorkerSetConf.WorkerService == "" {
		conf.WorkerSetConf.WorkerService = "trpc.ServingPaas.ConvertWorker.Http"
	}
}

func checkConfigJob(conf *Config) {
	const defCleanConvertTaskInterval = 1 * 60
	if conf.Job.Cleanup.CleanConvertTaskInterval < defCleanConvertTaskInterval {
		conf.Job.Cleanup.CleanConvertTaskInterval = defCleanConvertTaskInterval
	}
	const defConvertTaskExpireTime = 5 * 60
	if conf.Job.Cleanup.ConvertTaskExpireTime < defConvertTaskExpireTime {
		conf.Job.Cleanup.ConvertTaskExpireTime = defConvertTaskExpireTime
	}
}

func checkConfigTaskPriority(conf *Config) {
	const defPriority = 50
	const minPriority = 1
	const maxPriority = 100

	if conf.TaskPriority.DefaultPriority < minPriority || conf.TaskPriority.DefaultPriority > maxPriority {
		conf.TaskPriority.DefaultPriority = defPriority
	}
	for i := range conf.TaskPriority.Rules {
		if conf.TaskPriority.Rules[i].Priority < minPriority || conf.TaskPriority.Rules[i].Priority > maxPriority {
			conf.TaskPriority.Rules[i].Priority = defPriority
		}
	}
}

// checkConfig 检查配置合法性
func checkConfig(conf *Config) {
	log.Infof("Before check:%+v", conf)
	checkConfigDatabase(conf)
	checkConfigHeartbeat(conf)
	checkConfigWorkerSet(conf)
	checkConfigJob(conf)
	checkConfigTaskPriority(conf)
	log.Infof("Config after set:%+v", conf)
}

// setConfig 设置配置变量入口
func setConfig(conf *Config) {
	// 检查参数,设置最小默认值
	checkConfig(conf)
	gloConf.Store(conf)
}

func logConfigWithYaml(conf *Config) {
	// 用yaml的格式打印出来
	if yamlData, err := yaml.Marshal(conf); err != nil {
		log.Errorf("Failed to marshal config to YAML: %v", err)
	} else {
		log.Infof("Config after set:\n%s", string(yamlData))
	}
}
