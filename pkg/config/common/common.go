// Package common ...
package common

import (
	"fmt"
	"reflect"
)

// ValidateRequiredFields 通用结构体字段检查函数,返回所有空值字段的名称切片
func ValidateRequiredFields(obj interface{}, structName string) []string {
	v := reflect.ValueOf(obj)
	// 处理指针类型
	if v.Kind() == reflect.Ptr {
		v = v.Elem()
	}
	t := v.Type()
	var emptyFields []string

	for i := 0; i < v.NumField(); i++ {
		field := v.Field(i)
		fieldName := t.Field(i).Name
		fieldType := field.Kind()
		fullFieldName := fmt.Sprintf("%s.%s", structName, fieldName)

		// 处理嵌套结构体
		if fieldType == reflect.Struct {
			nestedFields := ValidateRequiredFields(field.Interface(), fullFieldName)
			if len(nestedFields) > 0 {
				emptyFields = append(emptyFields, nestedFields...)
			}
			continue
		}

		// 处理结构体指针
		if fieldType == reflect.Ptr {
			if field.IsNil() {
				emptyFields = append(emptyFields, fullFieldName)
				continue
			}
			if field.Elem().Kind() == reflect.Struct {
				nestedFields := ValidateRequiredFields(field.Interface(), fullFieldName)
				if len(nestedFields) > 0 {
					emptyFields = append(emptyFields, nestedFields...)
				}
				continue
			}
		}

		isEmpty := false
		switch fieldType {
		case reflect.String:
			isEmpty = field.String() == ""
		case reflect.Int, reflect.Int8, reflect.Int16, reflect.Int32, reflect.Int64:
			isEmpty = field.Int() == 0
			// 可以添加更多类型检查
		}

		if isEmpty {
			emptyFields = append(emptyFields, fullFieldName)
		}
	}

	return emptyFields
}
